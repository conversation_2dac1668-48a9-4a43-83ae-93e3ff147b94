import Foundation
import Alamofire
import SmartCodable

/// API服务层
class APIService {
    static let shared = APIService()
    private init() {}
    
//    private let defaultBaseURL = "https://test-youshu.gzyoushu.com/video"//测试
//    private let defaultBaseURL = "http://192.168.10.103:8020"//本地
//    private let defaultBaseURL = "https://www.gzsxq.com/videoPreRelease" // 正式沙箱环境
    private let defaultBaseURL = "https://www.gzsxq.com/video" //正式环境
    
    /// 发送请求并解析为指定模型
    func request<T: SmartCodable>(
        _ apiRequest: APIRequest,
        completion: @escaping (Result<T, APIError>) -> Void
    ) {
        // 使用请求中指定的baseURL或默认baseURL
        let baseURL = apiRequest.baseURL ?? defaultBaseURL
        let url = apiRequest.path.hasPrefix("http") ? apiRequest.path : baseURL + apiRequest.path
        
        // 将 APIMethod 转换为 Alamofire.HTTPMethod
        let method = Alamofire.HTTPMethod(rawValue: apiRequest.method.rawValue)

        // 确定编码方式
        let encoding: ParameterEncoding?
        if let apiEncoding = apiRequest.encoding {
            switch apiEncoding {
            case .json:
                encoding = JSONEncoding.default
            case .urlEncoded:
                encoding = URLEncoding.default
            }
        } else {
            encoding = nil // 使用默认编码
        }

        NetworkManager.shared.request(
            url: url,
            method: method,
            parameters: apiRequest.parameters,
            headers: nil,
            encoding: encoding
        ) { result in
            switch result {
            case .success(let data):
                // 打印原始JSON数据用于调试
//                if let jsonString = String(data: data, encoding: .utf8) {
//                    print("API Response JSON: \(jsonString)")
//                }
                
                // 使用 SmartCodable 解析数据
                if let model = T.deserialize(from: data) {
                    completion(.success(model))
                } else {
                    print("SmartCodable Decoding Error for model type: \(T.self)")
                    completion(.failure(.parseError))
                }
                
            case .failure(let error):
                completion(.failure(.networkError(error)))
            }
        }
    }
    
    /// 请求图片
    func requestImage(
        _ apiRequest: APIRequest,
        completion: @escaping (Result<Data, APIError>) -> Void
    ) {
        // 使用请求中指定的baseURL或默认baseURL
        let baseURL = apiRequest.baseURL ?? defaultBaseURL
        let url = apiRequest.path.hasPrefix("http") ? apiRequest.path : baseURL + apiRequest.path
        
        // 将 APIMethod 转换为 Alamofire.HTTPMethod
        let method = Alamofire.HTTPMethod(rawValue: apiRequest.method.rawValue)
        
        NetworkManager.shared.request(
            url: url,
            method: method,
            parameters: apiRequest.parameters,
            headers: nil
        ) { result in
            switch result {
            case .success(let data):
                if UIImage(data: data) != nil {
                    completion(.success(data))
                } else {
                    completion(.failure(.parseError))
                }
            case .failure(let error):
                completion(.failure(.networkError(error)))
            }
        }
    }
    
    /// 上传文件（支持file数组formData）
    func uploadFiles<T: SmartCodable>(
        _ apiRequest: APIRequest,
        files: [Data],
        completion: @escaping (Result<T, APIError>) -> Void
    ) {
        let baseURL = apiRequest.baseURL ?? defaultBaseURL
        let url = apiRequest.path.hasPrefix("http") ? apiRequest.path : baseURL + apiRequest.path
        // 使用默认请求头，但移除 Content-Type，让 Alamofire 自动设置 multipart/form-data 边界
        var headers = NetworkManager.shared.getDefaultHeaders()
        headers.remove(name: "Content-Type")
        Alamofire.AF.upload(multipartFormData: { multipartFormData in
            for (idx, fileData) in files.enumerated() {
                let fileName = "file_\(idx)_\(Int(Date().timeIntervalSince1970)).jpg"
                multipartFormData.append(fileData, withName: "file", fileName: fileName, mimeType: "image/jpeg")
            }
        }, to: url, headers: headers).responseData { response in
            switch response.result {
            case .success(let data):
                if let model = T.deserialize(from: data) {
                    completion(.success(model))
                } else {
                    completion(.failure(.parseError))
                }
            case .failure(let error):
                completion(.failure(.networkError(error)))
            }
        }
    }
    
    /// 直接POST原始Data数组（Content-Type: application/x-www-form-urlencoded，字段名file，支持文件数组）
    func uploadRawData<T: SmartCodable>(
        _ apiRequest: APIRequest,
        files: [Data],
        completion: @escaping (Result<T, APIError>) -> Void
    ) {
        let baseURL = apiRequest.baseURL ?? defaultBaseURL
        let url = apiRequest.path.hasPrefix("http") ? apiRequest.path : baseURL + apiRequest.path
        let headers: HTTPHeaders = ["Content-Type": "application/x-www-form-urlencoded"]
        // 构造body: file=...&file=... 形式
        var body = Data()
        for (idx, fileData) in files.enumerated() {
            if idx > 0 { body.append("&".data(using: .utf8)!) }
            body.append("file=".data(using: .utf8)!)
            body.append(fileData)
        }
        var urlRequest = URLRequest(url: URL(string: url)!)
        urlRequest.httpMethod = "POST"
        urlRequest.allHTTPHeaderFields = headers.dictionary
        urlRequest.httpBody = body
        AF.request(urlRequest).responseData { response in
            // 容错：后端无返回内容时直接返回空模型
            if let data = response.data, data.count > 0 {
                if let model = T.deserialize(from: data) {
                    completion(.success(model))
                } else {
                    completion(.failure(.parseError))
                }
            } else {
                // 兼容无内容返回
                if T.self == EmptyResponse.self, let empty = EmptyResponse() as? T {
                    completion(.success(empty))
                } else {
                    completion(.failure(.parseError))
                }
            }
        }
    }
}

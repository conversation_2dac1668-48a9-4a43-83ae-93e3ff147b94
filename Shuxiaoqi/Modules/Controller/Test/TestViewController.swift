//
//  TestViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/3/26.
//

import UIKit

class TestViewController: BaseViewController, UITableViewDelegate, UITableViewDataSource {
    
    // MARK: - Properties
    private let tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .plain)
        tableView.translatesAutoresizingMaskIntoConstraints = false
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "Cell")
        tableView.backgroundColor = .white
        return tableView
    }()
    
    // 测试用的模块名称数组
    private let moduleNames = [
        "金币活动中心",
        "带货商品列表",
        "功能测试3",
        "功能测试4",
        "功能测试5"
    ]
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 设置标题
        navTitle = "测试功能"
        
        // 设置视图背景色
        view.backgroundColor = .white
        
        // 设置表格视图
        setupTableView()
    }
    
    // MARK: - Setup
    private func setupTableView() {
        contentView.addSubview(tableView)
        
        // 设置 TableView 约束
        NSLayoutConstraint.activate([
            tableView.topAnchor.constraint(equalTo: contentView.safeAreaLayoutGuide.topAnchor),
            tableView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            tableView.bottomAnchor.constraint(equalTo: contentView.safeAreaLayoutGuide.bottomAnchor)
        ])
        
        // 设置代理
        tableView.delegate = self
        tableView.dataSource = self
    }
    
    // MARK: - UITableViewDataSource
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return moduleNames.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "Cell", for: indexPath)
        cell.textLabel?.text = moduleNames[indexPath.row]
        cell.accessoryType = .disclosureIndicator
        return cell
    }
    
    // MARK: - UITableViewDelegate
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 55
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        print("点击了测试模块：\(moduleNames[indexPath.row])")
        
        // 根据选择的行执行不同的操作
        switch indexPath.row {
        case 0:
            // 功能测试1
            let TaskVC = GoldCoinSystemTaskCenterViewController()
            navigationController?.pushViewController(TaskVC, animated: true)
        case 1:
            // 带货商品列表
            navigateToProductList()
        case 2:
            // 功能测试3
            showAlert(title: "功能测试3", message: "这是功能测试3的示例")
        case 3:
            // 功能测试4
            showAlert(title: "功能测试4", message: "这是功能测试4的示例")
        case 4:
            // 功能测试5
            showAlert(title: "功能测试5", message: "这是功能测试5的示例")
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    // 跳转到带货商品列表页面
    private func navigateToProductList() {
        let productListVC = PromotionCenterProductListViewController()
        navigationController?.pushViewController(productListVC, animated: true)
    }
}

//
//  PromotionCenterProductListViewController.swift
//  Shuxia<PERSON>qi
//
//  Created by yongsheng ye on 2025/3/26.
//

import UIKit
import SnapKit

class PromotionCenterProductListViewController: BaseViewController {
    
    // MARK: - Properties
    // 搜索栏
    private lazy var searchBar: UITextField = {
        let textField = UITextField()
        textField.placeholder = "搜索商品"
        textField.backgroundColor = UIColor(hex: "#EEEEEE")
        textField.layer.cornerRadius = 6
        textField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 15, height: 36))
        textField.leftViewMode = .always
        
        // 添加搜索图标
        let searchIconView = UIImageView(image: UIImage(named: "PromotionCenter_Search"))
        searchIconView.contentMode = .center
        searchIconView.frame = CGRect(x: 0, y: 0, width: 20, height: 20)
        
        // 创建一个容器视图，使图标与右侧保持12pt的距离
        let rightViewContainer = UIView(frame: CGRect(x: 0, y: 0, width: 32, height: 36))
        searchIconView.center = CGPoint(x: rightViewContainer.bounds.width - 12 - searchIconView.bounds.width/2, 
                                        y: rightViewContainer.bounds.height/2)
        rightViewContainer.addSubview(searchIconView)
        
        textField.rightView = rightViewContainer
        textField.rightViewMode = .always
        
        return textField
    }()
    
    // 分类滚动视图
    private lazy var categoryScrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsHorizontalScrollIndicator = false
        return scrollView
    }()
    
    // 分类容器视图
    private lazy var categoryContentView = UIView()
    
    // 排序按钮
    private lazy var sortButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("排序", for: .normal)
        button.setTitleColor(UIColor(hex: "#8C8C8C"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        return button
    }()
    
    // 排序图标
    private lazy var sortIcon: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "PromotionCenter_Sort"))
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // 商品列表
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = 10
        layout.minimumInteritemSpacing = 14 // 中间14pt间距
        let screenWidth = UIScreen.main.bounds.width
        let itemWidth = (screenWidth - 16 - 16 - 14) / 2 // 左右各16pt间距，中间14pt间距
        
        // 根据比例 164*266 计算高度
        let ratio: CGFloat = 266.0 / 164.0
        let itemHeight = itemWidth * ratio
        
        layout.itemSize = CGSize(width: itemWidth, height: itemHeight)
        layout.sectionInset = UIEdgeInsets(top: 10, left: 16, bottom: 10, right: 16)
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .white
        collectionView.register(PromotionCenterProductCell.self, forCellWithReuseIdentifier: "PromotionCenterProductCell")
        collectionView.delegate = self
        collectionView.dataSource = self
        return collectionView
    }()
    
    // 分类数据
    private let categories = ["全部", "服装", "美妆", "数码", "食品", "家居", "母婴", "运动", "图书"]
    private var selectedCategoryIndex = 0
    
    // 排序弹窗
    private lazy var sortPopupView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 4
        view.layer.shadowColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.1).cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowOpacity = 1
        view.layer.shadowRadius = 4
        view.layer.borderWidth = 0.5
        view.layer.borderColor = UIColor(hex: "#EEEEEE").cgColor
        view.isHidden = true
        return view
    }()
    
    // 排序选项
    private let sortOptions = ["销量高", "销量低", "佣金高", "佣金低", "最近售出"]
    private var selectedSortIndex = 0
    
    // 背景遮罩（用于点击空白区域关闭弹窗）
    private lazy var maskView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.1)
        view.isHidden = true
        
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(maskViewTapped))
        view.addGestureRecognizer(tapGesture)
        
        return view
    }()
    
    // 商品数据（示例数据）
    private var products: [[String: Any]] = [
        ["title": "2025春季新款连衣裙 气质淑女风收腰显瘦", "price": 20, "originalPrice": 299, "brand": "品牌1", "sales": "2.3w", "image": "dress_image"],
        ["title": "法国进口高端护肤精华液 补水保湿提亮", "price": 20, "originalPrice": 688, "brand": "品牌2", "sales": "1.8w", "image": "skincare_image"],
        ["title": "2025新款法国进口香水礼盒套装", "price": 20, "originalPrice": 299, "brand": "品牌1", "sales": "2.3w", "image": "perfume_image"],
        ["title": "2025新款轻奢真皮女士手提包", "price": 20, "originalPrice": 688, "brand": "品牌2", "sales": "1.8w", "image": "bag_image"]
    ]
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 设置标题
        navTitle = "商品列表"
        
        view.backgroundColor = .white
        // 设置视图背景色
        contentView.backgroundColor = .white
        
        // 设置UI
        setupUI()
        
        // 初始化时隐藏排序弹窗和遮罩
        sortPopupView.isHidden = true
        maskView.isHidden = true
        
        // 设置排序弹窗
        setupSortPopupView()
        
        // 模拟数据
        for _ in 0..<20 {
            products.append(["title": "2025春季新款连衣裙", "price": 20, "originalPrice": 299, "brand": "品牌1", "sales": "2.3w", "image": "dress_image"])
        }
    }
    
    // MARK: - Setup
    private func setupUI() {
        // 添加搜索栏
        contentView.addSubview(searchBar)
        searchBar.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(36)
        }
        
        // 添加分类滚动视图
        contentView.addSubview(categoryScrollView)
        categoryScrollView.snp.makeConstraints { make in
            make.top.equalTo(searchBar.snp.bottom).offset(8)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-71)
            make.height.equalTo(34)
        }
        
        // 添加分类内容视图
        categoryScrollView.addSubview(categoryContentView)
        categoryContentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(categoryScrollView)
        }
        
        // 添加排序图标
        contentView.addSubview(sortIcon)
        sortIcon.snp.makeConstraints { make in
            make.centerY.equalTo(categoryScrollView)
            make.right.equalToSuperview().offset(-16)
            make.width.height.equalTo(16)
        }
        
        // 添加排序按钮
        contentView.addSubview(sortButton)
        sortButton.snp.makeConstraints { make in
            make.centerY.equalTo(categoryScrollView)
            make.right.equalTo(sortIcon.snp.left).offset(-2)
            make.width.equalTo(34) // 增加宽度避免文字缩略
            make.height.equalTo(20)
        }
        
        // 添加点击事件
        let sortTapGesture = UITapGestureRecognizer(target: self, action: #selector(sortButtonTapped))
        sortButton.addGestureRecognizer(sortTapGesture)
        let sortIconTapGesture = UITapGestureRecognizer(target: self, action: #selector(sortButtonTapped))
        sortIcon.addGestureRecognizer(sortIconTapGesture)
        sortIcon.isUserInteractionEnabled = true
        
        // 添加背景遮罩
        view.addSubview(maskView)
        maskView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 添加排序弹窗
        view.addSubview(sortPopupView)
        sortPopupView.snp.makeConstraints { make in
            make.width.equalTo(58)
            make.height.equalTo(116)
            make.top.equalTo(sortIcon.snp.bottom)
            make.right.equalToSuperview().offset(-16)
        }
        
        // 添加商品列表
        contentView.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.top.equalTo(categoryScrollView.snp.bottom).offset(10)
            make.left.right.bottom.equalToSuperview()
        }
        
        // 设置分类按钮
        setupCategoryButtons()
    }
    
    private func setupCategoryButtons() {
        var lastButton: UIButton?
        var totalWidth: CGFloat = 0
        
        for (index, category) in categories.enumerated() {
            let button = UIButton(type: .custom)
            button.setTitle(category, for: .normal)
            button.setTitleColor(index == selectedCategoryIndex ? .black : UIColor.gray, for: .normal)
            button.titleLabel?.font = index == selectedCategoryIndex ? 
                UIFont.boldSystemFont(ofSize: 18) : UIFont.systemFont(ofSize: 14)
            button.tag = index
            button.addTarget(self, action: #selector(categoryButtonTapped(_:)), for: .touchUpInside)
            
            categoryContentView.addSubview(button)
            
            // 计算按钮宽度（文字宽度+10+10）
            let buttonWidth = category.size(withAttributes: [
                NSAttributedString.Key.font: UIFont.boldSystemFont(ofSize: 18)
            ]).width + 20 // 左右各10pt内边距
            
            button.snp.makeConstraints { make in
                make.top.bottom.equalToSuperview()
                make.width.equalTo(buttonWidth)
                if let lastButton = lastButton {
                    make.left.equalTo(lastButton.snp.right).offset(2) // 按钮之间2pt间隔
                } else {
                    make.left.equalToSuperview()
                }
            }
            
            // 为选中的分类添加下划线
            if index == selectedCategoryIndex {
                let underline = UIView()
                underline.backgroundColor = UIColor.orange
                underline.tag = 1000 + index // 用于后续识别
                categoryContentView.addSubview(underline)
                
                underline.snp.makeConstraints { make in
                    make.bottom.equalToSuperview()
                    make.centerX.equalTo(button)
                    make.width.equalTo(12)
                    make.height.equalTo(2)
                }
            }
            
            lastButton = button
            totalWidth += buttonWidth + (index < categories.count - 1 ? 2 : 0) // 添加按钮间距2pt
        }
        
        // 设置内容视图的宽度
        categoryContentView.snp.makeConstraints { make in
            make.width.equalTo(totalWidth)
        }
    }
    
    // MARK: - Setup Sort Popup
    private func setupSortPopupView() {
        // 移除所有现有的子视图
        for subview in sortPopupView.subviews {
            subview.removeFromSuperview()
        }

        // 计算每个按钮的高度 - 平均分配
        let buttonHeight = 116 / CGFloat(sortOptions.count)
        
        // 添加排序选项
        for (index, option) in sortOptions.enumerated() {
            let button = UIButton(type: .custom)
            button.setTitle(option, for: .normal)
            button.setTitleColor(index == selectedSortIndex ? .orange : UIColor(hex: "#3D3D3D"), for: .normal)
            button.titleLabel?.font = UIFont.systemFont(ofSize: 10) // 设置字体为10pt
            button.contentHorizontalAlignment = .center // 居中对齐
            button.tag = index
            button.addTarget(self, action: #selector(sortOptionTapped(_:)), for: .touchUpInside)
            
            sortPopupView.addSubview(button)
            button.snp.makeConstraints { make in
                make.left.right.equalToSuperview()
                make.height.equalTo(buttonHeight)
                make.top.equalToSuperview().offset(index * Int(buttonHeight))
            }
        }
    }
    
    // MARK: - Actions
    @objc private func categoryButtonTapped(_ sender: UIButton) {
        // 移除之前的下划线
        if let oldUnderline = categoryContentView.viewWithTag(1000 + selectedCategoryIndex) {
            oldUnderline.removeFromSuperview()
        }
        
        // 更新选中状态
        selectedCategoryIndex = sender.tag
        
        // 更新所有按钮的样式
        for subview in categoryContentView.subviews {
            if let button = subview as? UIButton {
                let isSelected = button.tag == selectedCategoryIndex
                button.setTitleColor(isSelected ? .black : UIColor.gray, for: .normal)
                button.titleLabel?.font = isSelected ? 
                    UIFont.boldSystemFont(ofSize: 18) : UIFont.systemFont(ofSize: 14)
            }
        }
        
        // 添加新的下划线
        let underline = UIView()
        underline.backgroundColor = UIColor.orange
        underline.tag = 1000 + selectedCategoryIndex
        categoryContentView.addSubview(underline)
        
        underline.snp.makeConstraints { make in
            make.bottom.equalToSuperview()
            make.centerX.equalTo(sender)
            make.width.equalTo(12)
            make.height.equalTo(2)
        }
        
        // 刷新商品列表
        collectionView.reloadData()
    }
    
    @objc private func sortButtonTapped() {
        // 显示遮罩
        maskView.isHidden = false
        view.bringSubviewToFront(maskView)

        // 显示排序弹窗
        sortPopupView.isHidden = false
        view.bringSubviewToFront(sortPopupView)
    }
    
    @objc private func maskViewTapped() {
        // 隐藏排序弹窗和遮罩
        sortPopupView.isHidden = true
        maskView.isHidden = true
    }
    
    @objc private func sortOptionTapped(_ sender: UIButton) {
        // 更新选中的排序选项
        selectedSortIndex = sender.tag
        
        // 更新排序按钮文字
        sortButton.setTitle(sortOptions[selectedSortIndex], for: .normal)
        
        // 更新所有选项的颜色
        for subview in sortPopupView.subviews {
            if let button = subview as? UIButton {
                button.setTitleColor(button.tag == selectedSortIndex ? .orange : UIColor(hex: "#3D3D3D"), for: .normal)
            }
        }
        
        // 隐藏排序弹窗和遮罩
        sortPopupView.isHidden = true
        maskView.isHidden = true
        
        // 刷新商品列表（根据排序选项）
        collectionView.reloadData()
    }
}

// MARK: - UICollectionViewDelegate, UICollectionViewDataSource
extension PromotionCenterProductListViewController: UICollectionViewDelegate, UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return products.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "PromotionCenterProductCell", for: indexPath) as! PromotionCenterProductCell
        let product = products[indexPath.item]
        cell.configure(with: product)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        print("选择了商品：\(products[indexPath.item]["title"] as? String ?? "")")
    }
}

// MARK: - PromotionCenterProductCell
class PromotionCenterProductCell: UICollectionViewCell {
    // 背景视图（用于添加阴影）
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 8
        // 添加阴影
        view.layer.shadowColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.10).cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowOpacity = 1
        view.layer.shadowRadius = 4
        view.layer.masksToBounds = false
        return view
    }()
    
    // 商品图片
    private let imageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.backgroundColor = UIColor(red: 0.95, green: 0.95, blue: 0.95, alpha: 1.0)
        imageView.layer.cornerRadius = 8
        return imageView
    }()
    
    // 商品标题
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = .black
        label.numberOfLines = 2
        return label
    }()
    
    // 价格标签
    private let priceLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.boldSystemFont(ofSize: 16)
        label.textColor = UIColor.orange
        return label
    }()
    
    // 原价标签
    private let originalPriceLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = .lightGray
        return label
    }()
    
    // 带货标签
    private let bringButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("带货", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 10)
        button.backgroundColor = UIColor.orange
        button.layer.cornerRadius = 4
        return button
    }()
    
    // 品牌图标
    private let brandIconView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.layer.cornerRadius = 8 // 圆形图标，直径16pt
        imageView.clipsToBounds = true
        imageView.backgroundColor = UIColor(hex: "#F5F5F5") // 默认背景色
        return imageView
    }()
    
    // 品牌标签
    private let brandLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = .darkGray
        return label
    }()
    
    // 销量标签
    private let salesLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = .lightGray
        label.textAlignment = .right
        return label
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        // 添加背景容器视图
        contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 添加子视图到容器视图
        containerView.addSubview(imageView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(priceLabel)
        containerView.addSubview(originalPriceLabel)
        containerView.addSubview(bringButton)
        containerView.addSubview(brandIconView)
        containerView.addSubview(brandLabel)
        containerView.addSubview(salesLabel)
        
        // 设置约束
        imageView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            // 保持图片区域的宽高比为 1:1
            make.height.equalTo(imageView.snp.width)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(8) // 增加间距
            make.left.right.equalToSuperview().inset(10) // 增加左右内边距
        }
        
        priceLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.height.equalTo(19)
            make.left.equalToSuperview().offset(10) // 与标题左对齐
        }
        
        originalPriceLabel.snp.makeConstraints { make in
            make.centerY.equalTo(priceLabel)
            make.left.equalTo(priceLabel.snp.right).offset(5)
        }
        
        bringButton.snp.makeConstraints { make in
            make.centerY.equalTo(priceLabel)
            make.right.equalToSuperview().offset(-10) // 与标题右对齐
            make.width.equalTo(44)
            make.height.equalTo(23)
        }
        
        // 品牌图标约束
        brandIconView.snp.makeConstraints { make in
            make.top.equalTo(priceLabel.snp.bottom).offset(10) // 增加间距
            make.left.equalToSuperview().offset(10) // 与标题左对齐
            make.width.height.equalTo(16) // 16*16的图标
            make.bottom.lessThanOrEqualToSuperview().offset(-10) // 确保底部有足够间距
        }
        
        // 品牌标签约束
        brandLabel.snp.makeConstraints { make in
            make.centerY.equalTo(brandIconView)
            make.left.equalTo(brandIconView.snp.right).offset(4) // 图标右侧4pt间距
            make.height.equalTo(14) // 固定高度14pt
            make.bottom.lessThanOrEqualToSuperview().offset(-10) // 确保底部有足够间距
        }
        
        salesLabel.snp.makeConstraints { make in
            make.centerY.equalTo(brandLabel)
            make.right.equalToSuperview().offset(-10) // 与标题右对齐
        }
    }
    
    func configure(with product: [String: Any]) {
        titleLabel.text = product["title"] as? String
        
        if let price = product["price"] as? Int {
            // 创建富文本字符串，设置不同部分的字体大小
            let priceText = NSMutableAttributedString()
            
            // ¥符号，10pt
            let yuanSymbol = NSAttributedString(string: "¥", attributes: [
                NSAttributedString.Key.font: UIFont.systemFont(ofSize: 10)
            ])
            priceText.append(yuanSymbol)
            
            // 金额数字，16pt Medium
            let priceNumber = NSAttributedString(string: "\(price)", attributes: [
                NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .medium)
            ])
            priceText.append(priceNumber)
            
            // 空格
            let space = NSAttributedString(string: " ")
            priceText.append(space)
            
            // 佣金文字，12pt
            let commission = NSAttributedString(string: "佣金", attributes: [
                NSAttributedString.Key.font: UIFont.systemFont(ofSize: 12)
            ])
            priceText.append(commission)
            
            priceLabel.attributedText = priceText
        }
        
        if let originalPrice = product["originalPrice"] as? Int {
            // 不添加删除线，只显示原价
            originalPriceLabel.text = "¥\(originalPrice)"
            originalPriceLabel.font = UIFont.systemFont(ofSize: 12)
            originalPriceLabel.textColor = .lightGray
        }
        
        // 设置品牌图标和名称
        brandLabel.text = product["brand"] as? String
        // 这里使用占位图，实际项目中应该根据品牌ID加载对应的品牌图标
        brandIconView.image = UIImage(named: "brand_icon_placeholder")
        
        salesLabel.text = "销量 \(product["sales"] as? String ?? "")" 
        
        // 设置图片（这里使用占位图，实际项目中应该使用网络图片加载库如Kingfisher）
        if let imageName = product["image"] as? String {
            imageView.image = UIImage(named: imageName) ?? UIImage(named: "placeholder_image")
        }
    }
}

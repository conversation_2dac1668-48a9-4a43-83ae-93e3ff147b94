//
//  StoreViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/3/19.
//

import UIKit
import WebKit

//首页商城

class StoreViewController: BaseViewController {

    // WebView控制器
    private var webViewController: WebViewController!

    override func viewDidLoad() {
        // 在调用super.viewDidLoad()之前隐藏导航栏
        showNavBar = false

        super.viewDidLoad()

        // 设置视图背景色
        view.backgroundColor = .white

        // 确保不会触发TabBar的显示/隐藏
        isTabBarRootViewController = false

        // 设置标题
        title = "商城"

        // 设置内容
        setupWebView()
    }

    // 覆盖 updateTabBarVisibility 方法，确保不会触发TabBar的隐藏
    override func updateTabBarVisibility() {
        // 不执行任何操作，避免触发TabBar的隐藏
        print("StoreViewController: 跳过TabBar可见性更新")
    }

    private func setupWebView() {
        // 创建WebViewController，使用空路径（商城首页）
        webViewController = WebViewController(path: "sxqVideo/shoppingIndex/shoppingIndex", title: "商城")

        // 强制隐藏WebView的导航栏，因为这是一级页面
        webViewController.hideNavBarAfterLoad = true
        // 添加为子控制器
        addChild(webViewController)
        view.addSubview(webViewController.view)

        // 设置WebView填充整个StoreViewController的视图区域
        webViewController.view.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            webViewController.view.topAnchor.constraint(equalTo: view.topAnchor),
            webViewController.view.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            webViewController.view.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            webViewController.view.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])

        webViewController.didMove(toParent: self)
    }
}

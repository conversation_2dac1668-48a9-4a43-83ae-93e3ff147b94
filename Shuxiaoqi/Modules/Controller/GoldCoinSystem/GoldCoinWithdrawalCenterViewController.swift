//
//  GoldCoinWithdrawalCenterViewController.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yong<PERSON>ng ye on 2025/8/13.
//
//  金币系统-提现

import UIKit
import SnapKit

// MARK: - 金币提现控制器
class GoldCoinWithdrawalCenterViewController: BaseViewController {

    // MARK: - 数据模型
    private var availableGoldCoins: Int = 8888
    private var exchangeRate: String = "10000金币=1元现金"

    // 提现金额选项
    private var withdrawalAmounts: [WithdrawalAmount] = [
        WithdrawalAmount(yuan: 50, goldCoins: 5000),
        WithdrawalAmount(yuan: 100, goldCoins: 10000),
        WithdrawalAmount(yuan: 200, goldCoins: 20000),
        WithdrawalAmount(yuan: 500, goldCoins: 50000)
    ]
    private var selectedAmountIndex: Int = 0 // 默认选中第一个

    // 提现账户选项
    private var withdrawalAccounts: [WithdrawalAccount] = [
        WithdrawalAccount(type: .alipay, displayName: "支付宝", accountInfo: "158****1111", isLinked: true),
        WithdrawalAccount(type: .wechat, displayName: "微信支付", accountInfo: "未绑定", isLinked: false)
    ]
    private var selectedAccountIndex: Int = 0 // 默认选中支付宝

    // 变动记录
    private var transactionRecords: [TransactionRecord] = [
        TransactionRecord(type: .reward, title: "视频奖励", amount: 500, status: .completed, date: "2025-04-05 12:00:00"),
        TransactionRecord(type: .withdrawal, title: "支付宝提现", amount: -5000, status: .completed, date: "2025-04-05 12:00:00"),
        TransactionRecord(type: .reward, title: "邀请奖励", amount: 2000, status: .completed, date: "2025-04-05 12:00:00")
    ]

    // MARK: - UI 组件

    // 滚动视图
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.backgroundColor = .white
        scrollView.showsVerticalScrollIndicator = false
        return scrollView
    }()

    // 滚动内容容器
    private lazy var scrollContentView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()

    // 可提现金币区域 - 卡片样式
    private lazy var availableCoinsView: UIView = {
        let view = UIView()
        view.applyCardStyle()
        return view
    }()

    private lazy var availableCoinsTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "可提现金币"
        label.textColor = UIColor(hex: "#333333")
        label.font = .systemFont(ofSize: 16, weight: .medium)
        return label
    }()

    private lazy var withdrawalInfoButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("提现说明", for: .normal)
        button.setTitleColor(UIColor(hex: "#999999"), for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 12)
        button.setImage(UIImage(named: "withdrawal_info_icon"), for: .normal)
        // 图标在左侧，文字在右侧（默认布局）
        button.titleEdgeInsets = UIEdgeInsets(top: 0, left: 4, bottom: 0, right: 0)
        button.addTarget(self, action: #selector(withdrawalInfoButtonTapped), for: .touchUpInside)
        return button
    }()

    private lazy var availableCoinsAmountLabel: UILabel = {
        let label = UILabel()
        label.text = "8,888"
        label.textColor = .appThemeOrange
        label.font = .systemFont(ofSize: 32, weight: .bold)
        return label
    }()

    private lazy var exchangeRateLabel: UILabel = {
        let label = UILabel()
        label.text = exchangeRate
        label.textColor = UIColor(hex: "#999999")
        label.font = .systemFont(ofSize: 14)
        return label
    }()

    // 提现金额区域 - 卡片样式
    private lazy var withdrawalAmountView: UIView = {
        let view = UIView()
        view.applyCardStyle()
        return view
    }()

    private lazy var withdrawalAmountTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "提现金额"
        label.textColor = UIColor(hex: "#333333")
        label.font = .systemFont(ofSize: 16, weight: .medium)
        return label
    }()

    private lazy var amountOptionsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 12
        stackView.distribution = .fillEqually
        return stackView
    }()

    // 提现账户区域 - 卡片样式
    private lazy var withdrawalAccountView: UIView = {
        let view = UIView()
        view.applyCardStyle()
        return view
    }()

    private lazy var withdrawalAccountTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "提现账户"
        label.textColor = UIColor(hex: "#333333")
        label.font = .systemFont(ofSize: 16, weight: .medium)
        return label
    }()

    private lazy var accountManageButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "arrow_right_gray"), for: .normal)
        button.addTarget(self, action: #selector(accountManageButtonTapped), for: .touchUpInside)
        return button
    }()

    private lazy var accountOptionsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 12
        stackView.distribution = .fillEqually
        return stackView
    }()

    // 变动记录区域
    private lazy var transactionRecordsView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()

    private lazy var transactionRecordsTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "变动记录"
        label.textColor = UIColor(hex: "#333333")
        label.font = .systemFont(ofSize: 16, weight: .medium)
        return label
    }()

    private lazy var viewAllRecordsButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("查看全部", for: .normal)
        button.setTitleColor(UIColor(hex: "#999999"), for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14)

        // 添加右箭头图标
        button.setImage(UIImage(named: "arrow_right_gray"), for: .normal)
        button.semanticContentAttribute = .forceRightToLeft // 图标在右侧
        button.imageEdgeInsets = UIEdgeInsets(top: 0, left: 4, bottom: 0, right: 0)

        button.addTarget(self, action: #selector(viewAllRecordsButtonTapped), for: .touchUpInside)
        return button
    }()

    private lazy var recordsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 0
        stackView.distribution = .fillEqually
        return stackView
    }()

    // 底部说明和按钮
    private lazy var bottomView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()

    private lazy var withdrawalNoticeLabel: UILabel = {
        let label = UILabel()
        label.text = "提现说明：\n1.提现金额将在1个工作日内到账\n2.如有疑问请联系客服\n3.平台保障资金安全，请放心提现"
        label.textColor = UIColor(hex: "#666666")
        label.font = .systemFont(ofSize: 12)
        label.numberOfLines = 0
        return label
    }()

    private lazy var confirmWithdrawalButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("确认提现", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        button.backgroundColor = .appThemeOrange
        button.layer.cornerRadius = 8
        button.addTarget(self, action: #selector(confirmWithdrawalButtonTapped), for: .touchUpInside)
        return button
    }()

    // MARK: - 生命周期
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupData()
    }

    // MARK: - 设置方法

    private func setupUI() {
        navTitle = "金币提现"
        contentView.backgroundColor = .white

        // 添加右侧更多按钮
        let moreButton = UIButton(type: .custom)
        moreButton.setImage(UIImage(named: "nav_more_dots"), for: .normal)
        moreButton.addTarget(self, action: #selector(moreButtonTapped), for: .touchUpInside)
        navRightItems = [moreButton]

        // 添加滚动视图
        contentView.addSubview(scrollView)
        scrollView.addSubview(scrollContentView)

        // 添加各个区域
        scrollContentView.addSubview(availableCoinsView)
        scrollContentView.addSubview(withdrawalAmountView)
        scrollContentView.addSubview(withdrawalAccountView)
        scrollContentView.addSubview(transactionRecordsView)
        scrollContentView.addSubview(bottomView)

        // 设置可提现金币区域
        availableCoinsView.addSubview(availableCoinsTitleLabel)
        availableCoinsView.addSubview(withdrawalInfoButton)
        availableCoinsView.addSubview(availableCoinsAmountLabel)
        availableCoinsView.addSubview(exchangeRateLabel)

        // 设置提现金额区域
        withdrawalAmountView.addSubview(withdrawalAmountTitleLabel)
        withdrawalAmountView.addSubview(amountOptionsStackView)

        // 设置提现账户区域
        withdrawalAccountView.addSubview(withdrawalAccountTitleLabel)
        withdrawalAccountView.addSubview(accountManageButton)
        withdrawalAccountView.addSubview(accountOptionsStackView)

        // 设置变动记录区域
        transactionRecordsView.addSubview(transactionRecordsTitleLabel)
        transactionRecordsView.addSubview(viewAllRecordsButton)
        transactionRecordsView.addSubview(recordsStackView)

        // 设置底部区域
        bottomView.addSubview(withdrawalNoticeLabel)
        bottomView.addSubview(confirmWithdrawalButton)

        setupConstraints()
    }

    private func setupData() {
        // 更新可提现金币显示
        updateAvailableCoinsDisplay()

        // 创建金额选项
        setupAmountOptions()

        // 创建账户选项
        setupAccountOptions()

        // 创建交易记录
        setupTransactionRecords()
    }

    private func updateAvailableCoinsDisplay() {
        // 格式化金币数量显示（添加千分位分隔符）
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        if let formattedNumber = formatter.string(from: NSNumber(value: availableGoldCoins)) {
            availableCoinsAmountLabel.text = formattedNumber
        } else {
            availableCoinsAmountLabel.text = "\(availableGoldCoins)"
        }
    }

    private func setupAmountOptions() {
        // 清除之前的选项
        amountOptionsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        // 创建两行，每行两个选项
        let firstRowStackView = UIStackView()
        firstRowStackView.axis = .horizontal
        firstRowStackView.spacing = 12
        firstRowStackView.distribution = .fillEqually

        let secondRowStackView = UIStackView()
        secondRowStackView.axis = .horizontal
        secondRowStackView.spacing = 12
        secondRowStackView.distribution = .fillEqually

        for (index, amount) in withdrawalAmounts.enumerated() {
            let optionView = createAmountOptionView(amount: amount, index: index)

            if index < 2 {
                firstRowStackView.addArrangedSubview(optionView)
            } else {
                secondRowStackView.addArrangedSubview(optionView)
            }
        }

        amountOptionsStackView.addArrangedSubview(firstRowStackView)
        amountOptionsStackView.addArrangedSubview(secondRowStackView)
    }

    private func setupAccountOptions() {
        // 清除之前的选项
        accountOptionsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        let accountRowStackView = UIStackView()
        accountRowStackView.axis = .horizontal
        accountRowStackView.spacing = 12
        accountRowStackView.distribution = .fillEqually

        for (index, account) in withdrawalAccounts.enumerated() {
            let optionView = createAccountOptionView(account: account, index: index)
            accountRowStackView.addArrangedSubview(optionView)
        }

        accountOptionsStackView.addArrangedSubview(accountRowStackView)
    }

    private func setupTransactionRecords() {
        // 清除之前的记录
        recordsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        for record in transactionRecords {
            let recordView = createTransactionRecordView(record: record)
            recordsStackView.addArrangedSubview(recordView)

            // 设置每个记录的固定高度为72pt
            recordView.snp.makeConstraints { make in
                make.height.equalTo(72)
            }
        }
    }

    private func setupConstraints() {
        // 滚动视图约束
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        scrollContentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        // 可提现金币区域约束 - 卡片样式，高度116
        availableCoinsView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(116)
        }

        availableCoinsTitleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
            make.height.equalTo(22)
        }

        withdrawalInfoButton.snp.makeConstraints { make in
            make.centerY.equalTo(availableCoinsTitleLabel)
            make.right.equalToSuperview().offset(-16)
            make.width.equalTo(68)
            make.height.equalTo(20) // 按钮高度
        }

        availableCoinsAmountLabel.snp.makeConstraints { make in
            make.top.equalTo(availableCoinsTitleLabel.snp.bottom).offset(8)
            make.left.equalToSuperview().offset(16)
        }

        exchangeRateLabel.snp.makeConstraints { make in
            make.top.equalTo(availableCoinsAmountLabel.snp.bottom).offset(8)
            make.left.equalToSuperview().offset(16)
            make.bottom.equalToSuperview().offset(-16)
        }

        // 提现金额区域约束 - 卡片样式，高度219
        withdrawalAmountView.snp.makeConstraints { make in
            make.top.equalTo(availableCoinsView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(219)
        }

        withdrawalAmountTitleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
            make.height.equalTo(22)
        }

        amountOptionsStackView.snp.makeConstraints { make in
            make.top.equalTo(withdrawalAmountTitleLabel.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }

        // 提现账户区域约束 - 卡片样式，高度148
        withdrawalAccountView.snp.makeConstraints { make in
            make.top.equalTo(withdrawalAmountView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(148)
        }

        withdrawalAccountTitleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
            make.height.equalTo(22)
        }

        accountManageButton.snp.makeConstraints { make in
            make.centerY.equalTo(withdrawalAccountTitleLabel)
            make.right.equalToSuperview().offset(-16)
            make.width.height.equalTo(16) // 右箭头图标大小为16*16
        }

        accountOptionsStackView.snp.makeConstraints { make in
            make.top.equalTo(withdrawalAccountTitleLabel.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }

        // 变动记录区域约束
        transactionRecordsView.snp.makeConstraints { make in
            make.top.equalTo(withdrawalAccountView.snp.bottom).offset(24)
            make.left.right.equalToSuperview().inset(16)
        }

        transactionRecordsTitleLabel.snp.makeConstraints { make in
            make.top.left.equalToSuperview()
            make.height.equalTo(22)
        }

        viewAllRecordsButton.snp.makeConstraints { make in
            make.centerY.equalTo(transactionRecordsTitleLabel)
            make.right.equalToSuperview()
        }

        recordsStackView.snp.makeConstraints { make in
            make.top.equalTo(transactionRecordsTitleLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }

        // 底部区域约束
        bottomView.snp.makeConstraints { make in
            make.top.equalTo(transactionRecordsView.snp.bottom).offset(24)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-20)
        }

        withdrawalNoticeLabel.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
        }

        confirmWithdrawalButton.snp.makeConstraints { make in
            make.top.equalTo(withdrawalNoticeLabel.snp.bottom).offset(20)
            make.left.right.equalToSuperview()
            make.height.equalTo(48)
            make.bottom.equalToSuperview()
        }
    }

    // MARK: - 创建选项视图

    private func createAmountOptionView(amount: WithdrawalAmount, index: Int) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .white
        containerView.layer.cornerRadius = 8
        containerView.layer.borderWidth = 1

        // 设置选中状态的边框颜色
        if index == selectedAmountIndex {
            containerView.layer.borderColor = UIColor.appThemeOrange.cgColor
        } else {
            containerView.layer.borderColor = UIColor(hex: "#E5E5E5").cgColor
        }

        // 金额标签
        let amountLabel = UILabel()
        amountLabel.text = "¥\(amount.yuan)"
        // 根据选中状态设置文字颜色
        amountLabel.textColor = index == selectedAmountIndex ? .appThemeOrange : UIColor(hex: "#333333")
        amountLabel.font = .systemFont(ofSize: 18, weight: .medium)
        amountLabel.textAlignment = .center

        // 金币数量标签
        let coinsLabel = UILabel()
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        let formattedCoins = formatter.string(from: NSNumber(value: amount.goldCoins)) ?? "\(amount.goldCoins)"
        coinsLabel.text = "\(formattedCoins)金币"
        coinsLabel.textColor = UIColor(hex: "#999999")
        coinsLabel.font = .systemFont(ofSize: 12)
        coinsLabel.textAlignment = .center

        containerView.addSubview(amountLabel)
        containerView.addSubview(coinsLabel)

        amountLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(12)
        }

        coinsLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(amountLabel.snp.bottom).offset(4)
            make.bottom.equalToSuperview().offset(-12)
        }

        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(amountOptionTapped(_:)))
        containerView.addGestureRecognizer(tapGesture)
        containerView.tag = index

        return containerView
    }

    private func createAccountOptionView(account: WithdrawalAccount, index: Int) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .white
        containerView.layer.cornerRadius = 8
        containerView.layer.borderWidth = 1

        // 设置选中状态的边框颜色
        if index == selectedAccountIndex && account.isLinked {
            containerView.layer.borderColor = UIColor.appThemeOrange.cgColor
        } else {
            containerView.layer.borderColor = UIColor(hex: "#E5E5E5").cgColor
        }

        // 账户图标
        let iconImageView = UIImageView()
        let iconName = account.type == .alipay ? "withdrawal_alipay_icon" : "withdrawal_wechat_icon"
        iconImageView.image = UIImage(named: iconName)
        iconImageView.contentMode = .scaleAspectFit

        // 账户名称标签
        let nameLabel = UILabel()
        nameLabel.text = account.displayName
        nameLabel.textColor = UIColor(hex: "#000000", alpha: 0.85)
        nameLabel.font = .systemFont(ofSize: 12, weight: .medium)

        // 账户信息标签
        let infoLabel = UILabel()
        infoLabel.text = account.accountInfo
        infoLabel.textColor = account.isLinked ? UIColor(hex: "#666666") : UIColor(hex: "#000000", alpha: 0.65)
        infoLabel.font = .systemFont(ofSize: 10)

        containerView.addSubview(iconImageView)
        containerView.addSubview(nameLabel)
        containerView.addSubview(infoLabel)

        iconImageView.snp.makeConstraints { make in
            make.left.top.equalToSuperview().offset(10)
            make.width.height.equalTo(20) // 支付宝和微信图标大小为20*20
        }

        nameLabel.snp.makeConstraints { make in
            make.left.equalTo(iconImageView.snp.left)
            make.top.equalTo(iconImageView.snp.bottom).offset(4)
            make.height.equalTo(13)
        }

        infoLabel.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.top.equalTo(nameLabel.snp.bottom).offset(2)
            make.right.equalTo(nameLabel)
            make.bottom.equalToSuperview().offset(-10)
        }

        // 添加点击手势（所有账户都可以点击）
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(accountOptionTapped(_:)))
        containerView.addGestureRecognizer(tapGesture)
        containerView.tag = index

        // 未绑定账户显示为灰色，但仍可点击
        if !account.isLinked {
            containerView.alpha = 0.6
        }

        return containerView
    }

    private func createTransactionRecordView(record: TransactionRecord) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .white

        // 标题标签
        let titleLabel = UILabel()
        titleLabel.text = record.title
        titleLabel.textColor = UIColor(hex: "#333333")
        titleLabel.font = .systemFont(ofSize: 14, weight: .medium)

        // 日期标签
        let dateLabel = UILabel()
        dateLabel.text = formatDate(record.date)
        dateLabel.textColor = UIColor(hex: "#999999")
        dateLabel.font = .systemFont(ofSize: 12)

        // 金额标签
        let amountLabel = UILabel()
        let amountText = record.amount > 0 ? "+\(record.amount)金币" : "\(record.amount)金币"
        amountLabel.text = amountText
        amountLabel.textColor = record.amount > 0 ? UIColor(hex: "#FF6B6B") : UIColor(hex: "#4CAF50")
        amountLabel.font = .systemFont(ofSize: 14, weight: .medium)
        amountLabel.textAlignment = .right

        // 状态标签
        let statusLabel = UILabel()
        statusLabel.text = record.status.displayText
        statusLabel.textColor = UIColor(hex: "#999999")
        statusLabel.font = .systemFont(ofSize: 12)
        statusLabel.textAlignment = .right

        containerView.addSubview(titleLabel)
        containerView.addSubview(dateLabel)
        containerView.addSubview(amountLabel)
        containerView.addSubview(statusLabel)

        titleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.top.equalToSuperview().offset(16)
        }

        dateLabel.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.bottom.equalToSuperview().offset(-16)
        }

        amountLabel.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.centerY.equalTo(titleLabel)
        }

        statusLabel.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.centerY.equalTo(dateLabel)
        }

        // 添加分割线
        let separatorLine = UIView()
        separatorLine.backgroundColor = UIColor(hex: "#F5F5F5")
        containerView.addSubview(separatorLine)

        separatorLine.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(1)
        }

        return containerView
    }

    // MARK: - 辅助方法

    private func formatDate(_ dateString: String) -> String {
        // 简单的日期格式化，实际项目中可能需要更复杂的处理
        let components = dateString.components(separatedBy: " ")
        if components.count >= 2 {
            return components[0] + " " + components[1]
        }
        return dateString
    }

    private func updateAmountSelection(_ newIndex: Int) {
        selectedAmountIndex = newIndex
        setupAmountOptions() // 重新创建选项以更新选中状态
    }

    private func updateAccountSelection(_ newIndex: Int) {
        selectedAccountIndex = newIndex
        setupAccountOptions() // 重新创建选项以更新选中状态
    }

    // MARK: - 事件处理

    @objc private func moreButtonTapped() {
        print("更多按钮被点击")
        // TODO: 显示更多选项
        let alert = UIAlertController(title: "更多", message: "功能开发中", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    @objc private func withdrawalInfoButtonTapped() {
        print("提现说明按钮被点击")
        // TODO: 显示提现说明
        let alert = UIAlertController(title: "提现说明", message: "提现相关规则和说明", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    @objc private func accountManageButtonTapped() {
        print("账户管理按钮被点击")
        // TODO: 跳转到账户管理页面
        let alert = UIAlertController(title: "账户管理", message: "跳转到账户管理页面", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    @objc private func viewAllRecordsButtonTapped() {
        print("查看全部记录按钮被点击")
        // TODO: 跳转到完整的交易记录页面
        let alert = UIAlertController(title: "交易记录", message: "跳转到完整的交易记录页面", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    @objc private func amountOptionTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        let index = view.tag

        if index != selectedAmountIndex {
            updateAmountSelection(index)
            print("选择提现金额：¥\(withdrawalAmounts[index].yuan)")
        }
    }

    @objc private func accountOptionTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        let index = view.tag
        let account = withdrawalAccounts[index]

        if account.isLinked {
            // 已绑定账户：选择该账户
            if index != selectedAccountIndex {
                updateAccountSelection(index)
                print("选择提现账户：\(account.displayName)")
            }
        } else {
            // 未绑定账户：跳转到绑定页面
            print("跳转到\(account.displayName)绑定页面")
            showAccountBindingPage(for: account.type)
        }
    }

    private func showAccountBindingPage(for accountType: WithdrawalAccountType) {
        let accountTypeName = accountType == .alipay ? "支付宝" : "微信支付"
        let alert = UIAlertController(title: "绑定\(accountTypeName)", message: "跳转到\(accountTypeName)绑定页面", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)

        // TODO: 实际项目中这里应该跳转到对应的绑定页面
        // 例如：
        // let bindingVC = AccountBindingViewController(accountType: accountType)
        // navigationController?.pushViewController(bindingVC, animated: true)
    }

    @objc private func confirmWithdrawalButtonTapped() {
        print("确认提现按钮被点击")

        let selectedAmount = withdrawalAmounts[selectedAmountIndex]
        let selectedAccount = withdrawalAccounts[selectedAccountIndex]

        // 检查金币是否足够
        if availableGoldCoins < selectedAmount.goldCoins {
            let alert = UIAlertController(title: "提示", message: "金币余额不足", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
            return
        }

        // 检查账户是否已绑定
        if !selectedAccount.isLinked {
            let alert = UIAlertController(title: "提示", message: "请先绑定提现账户", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
            return
        }

        // 显示确认对话框
        let message = "确认提现¥\(selectedAmount.yuan)到\(selectedAccount.displayName)(\(selectedAccount.accountInfo))？"
        let alert = UIAlertController(title: "确认提现", message: message, preferredStyle: .alert)

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(UIAlertAction(title: "确认", style: .default) { [weak self] _ in
            self?.performWithdrawal()
        })

        present(alert, animated: true)
    }

    private func performWithdrawal() {
        let selectedAmount = withdrawalAmounts[selectedAmountIndex]

        // 扣除金币
        availableGoldCoins -= selectedAmount.goldCoins
        updateAvailableCoinsDisplay()

        // 添加提现记录
        let newRecord = TransactionRecord(
            type: .withdrawal,
            title: "\(withdrawalAccounts[selectedAccountIndex].displayName)提现",
            amount: -selectedAmount.goldCoins,
            status: .processing,
            date: getCurrentDateString()
        )
        transactionRecords.insert(newRecord, at: 0)
        setupTransactionRecords()

        // 显示成功提示
        let alert = UIAlertController(title: "提现申请成功", message: "您的提现申请已提交，预计1个工作日内到账", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func getCurrentDateString() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        return formatter.string(from: Date())
    }
}

// MARK: - 数据模型

/// 提现金额选项
struct WithdrawalAmount {
    let yuan: Int        // 人民币金额
    let goldCoins: Int   // 对应的金币数量
}

/// 提现账户类型
enum WithdrawalAccountType {
    case alipay  // 支付宝
    case wechat  // 微信支付
}

/// 提现账户
struct WithdrawalAccount {
    let type: WithdrawalAccountType
    let displayName: String    // 显示名称
    let accountInfo: String    // 账户信息
    let isLinked: Bool        // 是否已绑定
}

/// 交易记录类型
enum TransactionType {
    case reward      // 奖励
    case withdrawal  // 提现
}

/// 交易状态
enum TransactionStatus {
    case completed   // 已完成
    case processing  // 处理中
    case failed      // 失败

    var displayText: String {
        switch self {
        case .completed:
            return "已到账"
        case .processing:
            return "处理中"
        case .failed:
            return "失败"
        }
    }
}

/// 交易记录
struct TransactionRecord {
    let type: TransactionType
    let title: String           // 标题
    let amount: Int            // 金额（正数为收入，负数为支出）
    let status: TransactionStatus
    let date: String           // 日期
}

// MARK: - UIView 卡片样式扩展
extension UIView {

    /// 应用卡片样式（圆角 + 阴影）
    func applyCardStyle() {
        backgroundColor = .white
        layer.cornerRadius = 12
        layer.shadowColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.1).cgColor
        layer.shadowOffset = CGSize(width: 0, height: 2)
        layer.shadowOpacity = 1
        layer.shadowRadius = 4
    }
}

# 金币提现页面卡片样式实现

## 📋 实现概述

根据设计稿要求，将金币提现页面的三个主要区域实现为卡片样式：
- 可提现金币区域（高度：116pt）
- 提现金额区域（高度：219pt）  
- 提现账户区域（高度：148pt）

## 🎨 卡片样式规范

### 视觉效果
- **圆角**：12pt
- **阴影颜色**：黑色，透明度 10%
- **阴影偏移**：(0, 2)
- **阴影模糊半径**：4pt
- **背景色**：白色

### 参考代码
基于您提供的设计平台生成代码：
```swift
// 阴影和圆角效果
view.layer.cornerRadius = 12
view.layer.shadowColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.1).cgColor
view.layer.shadowOffset = CGSize(width: 0, height: 2)
view.layer.shadowOpacity = 1
view.layer.shadowRadius = 4
```

## 🛠️ 技术实现

### 1. 卡片样式扩展
创建了 UIView 扩展，统一管理卡片样式：

```swift
extension UIView {
    /// 应用卡片样式（圆角 + 阴影）
    func applyCardStyle() {
        backgroundColor = .white
        layer.cornerRadius = 12
        layer.shadowColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.1).cgColor
        layer.shadowOffset = CGSize(width: 0, height: 2)
        layer.shadowOpacity = 1
        layer.shadowRadius = 4
    }
}
```

### 2. 卡片视图创建
简化了卡片视图的创建代码：

```swift
// 可提现金币区域 - 卡片样式
private lazy var availableCoinsView: UIView = {
    let view = UIView()
    view.applyCardStyle()
    return view
}()
```

### 3. 布局约束调整
根据设计稿高度要求调整了约束：

```swift
// 可提现金币区域 - 高度116pt
availableCoinsView.snp.makeConstraints { make in
    make.top.equalToSuperview().offset(16)
    make.left.right.equalToSuperview().inset(16)
    make.height.equalTo(116)
}

// 提现金额区域 - 高度219pt  
withdrawalAmountView.snp.makeConstraints { make in
    make.top.equalTo(availableCoinsView.snp.bottom).offset(16)
    make.left.right.equalToSuperview().inset(16)
    make.height.equalTo(219)
}

// 提现账户区域 - 高度148pt
withdrawalAccountView.snp.makeConstraints { make in
    make.top.equalTo(withdrawalAmountView.snp.bottom).offset(16)
    make.left.right.equalToSuperview().inset(16)
    make.height.equalTo(148)
}
```

## 📐 内边距调整

为了适配卡片样式，调整了内部元素的边距：

### 标题和按钮
- **左边距**：16pt（从卡片边缘开始）
- **右边距**：16pt（到卡片边缘）
- **顶部边距**：16pt（从卡片顶部开始）
- **底部边距**：16pt（到卡片底部）

### 内容区域
- **StackView 边距**：左右各16pt，确保内容不贴边

## 🎯 设计优势

### 1. 视觉层次
- 卡片阴影创造了层次感
- 白色背景与页面背景形成对比
- 圆角设计更加现代化

### 2. 用户体验
- 清晰的功能区域划分
- 更好的内容组织
- 符合现代移动应用设计趋势

### 3. 代码维护
- 统一的样式管理
- 可复用的卡片样式扩展
- 清晰的布局结构

## 📱 最终效果

三个卡片区域垂直排列，每个卡片之间间距16pt：
1. **可提现金币卡片**：显示当前金币余额和兑换比例
2. **提现金额卡片**：2x2网格的金额选择选项
3. **提现账户卡片**：支付宝和微信支付账户选择

每个卡片都具有统一的视觉风格，提供了良好的用户体验和清晰的信息层次。

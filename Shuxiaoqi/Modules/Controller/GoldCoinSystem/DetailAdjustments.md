# 金币提现页面细节调整

## 📋 本次调整内容

### 1. 可提现金币区域 - 提现说明按钮
**调整前**：只有一个图标按钮
**调整后**：文字 + 图标的组合按钮

#### 实现细节
```swift
private lazy var withdrawalInfoButton: UIButton = {
    let button = UIButton(type: .custom)
    button.setTitle("提现说明", for: .normal)
    button.setTitleColor(UIColor(hex: "#999999"), for: .normal)
    button.titleLabel?.font = .systemFont(ofSize: 12)
    button.setImage(UIImage(named: "withdrawal_info_icon"), for: .normal)
    button.semanticContentAttribute = .forceRightToLeft // 图标在右侧
    button.imageEdgeInsets = UIEdgeInsets(top: 0, left: 4, bottom: 0, right: 0)
    return button
}()
```

#### 视觉效果
- **文字**："提现说明"，颜色 #999999，字体大小 12pt
- **图标**：位于文字右侧，16×16pt
- **间距**：文字和图标之间4pt间距

### 2. 变动记录区域 - Cell高度调整
**调整前**：动态高度
**调整后**：固定72pt高度

#### 实现细节
```swift
// 在setupTransactionRecords方法中
recordView.snp.makeConstraints { make in
    make.height.equalTo(72)
}

// 内部元素间距调整
titleLabel.snp.makeConstraints { make in
    make.top.equalToSuperview().offset(16) // 顶部间距16pt
}

dateLabel.snp.makeConstraints { make in
    make.bottom.equalToSuperview().offset(-16) // 底部间距16pt
}
```

#### 布局规范
- **Cell总高度**：72pt
- **顶部间距**：16pt
- **底部间距**：16pt
- **内容区域**：40pt (72 - 16 - 16)

### 3. 变动记录区域 - 标题间距调整
**调整前**：标题距离第一个cell 16pt
**调整后**：标题距离第一个cell 12pt

#### 实现细节
```swift
recordsStackView.snp.makeConstraints { make in
    make.top.equalTo(transactionRecordsTitleLabel.snp.bottom).offset(12)
    // 其他约束保持不变
}
```

## 🎨 视觉效果对比

### 提现说明按钮
```
调整前：[?]
调整后：提现说明 [?]
```

### 变动记录区域
```
调整前：
标题
    ↓ 16pt
[动态高度Cell]

调整后：
标题  
    ↓ 12pt
[72pt固定高度Cell]
```

## 📐 精确尺寸规范

### 可提现金币卡片
- **卡片高度**：116pt
- **提现说明按钮**：
  - 高度：20pt
  - 文字大小：12pt
  - 图标大小：16×16pt
  - 文字图标间距：4pt

### 变动记录区域
- **标题到第一个Cell**：12pt
- **每个Cell高度**：72pt
- **Cell内部间距**：
  - 顶部：16pt
  - 底部：16pt
  - 左右：0pt（继承父容器的16pt）

## 🔧 技术实现要点

### 1. 按钮文字图标布局
使用 `semanticContentAttribute = .forceRightToLeft` 让图标显示在文字右侧，这是iOS推荐的做法。

### 2. 固定Cell高度
通过在StackView中为每个子视图设置固定高度约束，确保所有记录Cell高度一致。

### 3. 间距精确控制
使用SnapKit的offset方法精确控制各元素间的间距，确保符合设计稿要求。

## 🔧 问题修复

### 问题1：提现说明图标位置
**问题**：图标显示在文字右侧
**修复**：调整为图标在文字左侧

```swift
// 修复前
button.semanticContentAttribute = .forceRightToLeft // 图标在右侧
button.imageEdgeInsets = UIEdgeInsets(top: 0, left: 4, bottom: 0, right: 0)

// 修复后
button.titleEdgeInsets = UIEdgeInsets(top: 0, left: 4, bottom: 0, right: 0) // 文字右移4pt
```

### 问题2：微信未绑定时的点击事件
**问题**：未绑定账户无法点击
**修复**：为未绑定账户添加点击事件，跳转到绑定页面

```swift
// 修复前
if account.isLinked {
    let tapGesture = UITapGestureRecognizer(target: self, action: #selector(accountOptionTapped(_:)))
    containerView.addGestureRecognizer(tapGesture)
}

// 修复后
let tapGesture = UITapGestureRecognizer(target: self, action: #selector(accountOptionTapped(_:)))
containerView.addGestureRecognizer(tapGesture) // 所有账户都可点击

// 点击事件处理
if account.isLinked {
    // 选择账户
    updateAccountSelection(index)
} else {
    // 跳转到绑定页面
    showAccountBindingPage(for: account.type)
}
```

## ✅ 验证清单

- [x] 提现说明按钮显示文字和图标
- [x] 图标位于文字左侧，间距4pt
- [x] 变动记录Cell高度固定为72pt
- [x] 标题距离第一个Cell为12pt
- [x] Cell内部元素垂直居中对齐
- [x] 所有间距符合设计规范
- [x] 微信未绑定时可点击跳转到绑定页面
- [x] 已绑定账户可正常选择

## 📱 最终效果

现在的金币提现页面完全符合设计稿的细节要求：
1. 可提现金币区域有完整的"提现说明"按钮
2. 变动记录区域有统一的72pt Cell高度
3. 所有间距都精确匹配设计稿
4. 视觉层次清晰，用户体验良好

//
//  GoldCoinWithdrawalTestViewController.swift
//  Shuxia<PERSON>qi
//
//  Created by yong<PERSON><PERSON> ye on 2025/8/13.
//
//  金币提现页面测试控制器

import UIKit
import SnapKit

// MARK: - 金币提现测试控制器
class GoldCoinWithdrawalTestViewController: BaseViewController {
    
    // MARK: - UI 组件
    
    private lazy var testButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("测试金币提现页面", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        button.backgroundColor = .appThemeOrange
        button.layer.cornerRadius = 8
        button.addTarget(self, action: #selector(testButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var taskCenterButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("打开任务中心", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        button.backgroundColor = .appThemeOrange
        button.layer.cornerRadius = 8
        button.addTarget(self, action: #selector(taskCenterButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var infoLabel: UILabel = {
        let label = UILabel()
        label.text = "点击按钮测试金币提现页面功能\n\n功能包括：\n• 可提现金币显示\n• 提现金额选择\n• 提现账户管理\n• 交易记录查看\n• 完整提现流程"
        label.textColor = UIColor(hex: "#666666")
        label.font = .systemFont(ofSize: 14)
        label.numberOfLines = 0
        label.textAlignment = .center
        return label
    }()
    
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    // MARK: - 设置方法
    
    private func setupUI() {
        navTitle = "金币提现测试"
        contentView.backgroundColor = .white
        
        contentView.addSubview(infoLabel)
        contentView.addSubview(testButton)
        contentView.addSubview(taskCenterButton)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        infoLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-80)
            make.left.right.equalToSuperview().inset(40)
        }
        
        testButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(infoLabel.snp.bottom).offset(40)
            make.left.right.equalToSuperview().inset(40)
            make.height.equalTo(48)
        }
        
        taskCenterButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(testButton.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(40)
            make.height.equalTo(48)
        }
    }
    
    // MARK: - 事件处理
    
    @objc private func testButtonTapped() {
        print("测试金币提现页面")
        let withdrawalVC = GoldCoinWithdrawalCenterViewController()
        navigationController?.pushViewController(withdrawalVC, animated: true)
    }
    
    @objc private func taskCenterButtonTapped() {
        print("打开任务中心")
        let taskCenterVC = GoldCoinSystemTaskCenterViewController()
        navigationController?.pushViewController(taskCenterVC, animated: true)
    }
}

// MARK: - 测试用的 SceneDelegate 扩展
/*
 在 SceneDelegate.swift 中可以这样使用：
 
 func scene(_ scene: UIScene, willConnectTo session: UISceneSession, options connectionOptions: UIScene.ConnectionOptions) {
     guard let windowScene = (scene as? UIWindowScene) else { return }
     
     window = UIWindow(windowScene: windowScene)
     
     // 测试金币提现页面
     let testVC = GoldCoinWithdrawalTestViewController()
     let navController = UINavigationController(rootViewController: testVC)
     
     window?.rootViewController = navController
     window?.makeKeyAndVisible()
 }
 */

//
//  VideoEditTestViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/4/7.
//

import UIKit
import TXLiteAVSDK_UGC
import Photos
import TXLiteAVSDK_UGC.TXVideoEditer
import AVFoundation

class VideoEditTestViewController: UIViewController, TXVideoGenerateListener, UIImagePickerControllerDelegate & UINavigationControllerDelegate {
    
    // MARK: - Properties
    var videoPath: String?
    private var videoEditor: TXVideoEditer?
    private var isPlaying = false
    private var isGenerating = false
    private var thumbnailArray: [UIImage] = []
    private var videoDuration: CGFloat = 0
    private var startTime: CGFloat = 0
    private var endTime: CGFloat = 0
    private var generatedVideoPath: String?
    
    // 视频旋转相关属性
    private var currentRotation: Int32 = 0
    private var rotationButtons: [UIButton] = []
    
    // BGM相关属性
    private var bgmPath: String?
    private var bgmVolume: Float = 1.0
    private var videoVolume: Float = 1.0
    private var bgmStartTime: Float = 0
    private var bgmEndTime: Float = 0
    private var bgmFadeInDuration: Float = 1.0  // 淡入时长
    private var bgmFadeOutDuration: Float = 1.0  // 淡出时长
    
    private var playbackTimer: Timer?
    private var lastPlaybackTime: CGFloat = 0
    
    // 缩略图滚动视图
    private lazy var thumbnailScrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.showsVerticalScrollIndicator = false
        scrollView.backgroundColor = .black
        scrollView.isUserInteractionEnabled = true
        return scrollView
    }()
    
    // 裁剪范围视图
    private lazy var trimView: VideoTrimView = {
        let view = VideoTrimView()
        view.delegate = self
        return view
    }()
    
    // 视频预览视图
    private lazy var previewView: UIView = {
        let view = UIView()
        view.backgroundColor = .black
        return view
    }()
    
    // 主滚动视图
    private lazy var mainScrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.backgroundColor = .systemBackground
        scrollView.showsVerticalScrollIndicator = true
        scrollView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 20, right: 0)
        return scrollView
    }()
    
    // 滚动内容容器
    private lazy var scrollContentView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        return view
    }()
    
    // 按钮堆栈容器
    private lazy var buttonStacksContainer: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 20
        stackView.distribution = .equalSpacing
        return stackView
    }()
    
    // 播放/暂停按钮
    private lazy var playButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "play.fill"), for: .normal)
        button.tintColor = .white
        button.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        button.layer.cornerRadius = 25
        button.addTarget(self, action: #selector(playButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 生成按钮
    private lazy var generateButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("生成视频", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .systemBlue
        button.layer.cornerRadius = 8
        button.addTarget(self, action: #selector(generateButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 返回按钮
    private lazy var backButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("返回", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .darkGray
        button.layer.cornerRadius = 8
        button.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 保存按钮
    private lazy var saveButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("保存到相册", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .systemGreen
        button.layer.cornerRadius = 8
        button.addTarget(self, action: #selector(saveButtonTapped), for: .touchUpInside)
        button.isEnabled = false
        return button
    }()
    
    // 生成进度条
    private lazy var progressView: UIProgressView = {
        let progress = UIProgressView(progressViewStyle: .default)
        progress.progressTintColor = .systemBlue
        progress.trackTintColor = .darkGray
        progress.progress = 0
        progress.isHidden = true
        return progress
    }()
    
    // 进度标签
    private lazy var progressLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.textAlignment = .center
        label.text = "0%"
        label.font = .systemFont(ofSize: 12)
        label.isHidden = true
        return label
    }()
    
    // 时间范围标签
    private lazy var timeRangeLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.textAlignment = .center
        label.font = .systemFont(ofSize: 14)
        label.text = "00:00 - 00:00"
        return label
    }()
    
    // BGM测试按钮
    private lazy var bgmTestButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("测试BGM", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .systemBlue
        button.layer.cornerRadius = 8
        button.addTarget(self, action: #selector(testBgmButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // BGM音量滑块
    private lazy var bgmVolumeSlider: UISlider = {
        let slider = UISlider()
        slider.minimumValue = 0
        slider.maximumValue = 1
        slider.value = bgmVolume
        slider.addTarget(self, action: #selector(bgmVolumeChanged(_:)), for: .valueChanged)
        return slider
    }()
    
    // 视频音量滑块
    private lazy var videoVolumeSlider: UISlider = {
        let slider = UISlider()
        slider.minimumValue = 0
        slider.maximumValue = 1
        slider.value = videoVolume
        slider.addTarget(self, action: #selector(videoVolumeChanged(_:)), for: .valueChanged)
        return slider
    }()
    
    // BGM淡入时长滑块
    private lazy var bgmFadeInSlider: UISlider = {
        let slider = UISlider()
        slider.minimumValue = 0
        slider.maximumValue = 5
        slider.value = bgmFadeInDuration
        slider.addTarget(self, action: #selector(bgmFadeInChanged(_:)), for: .valueChanged)
        return slider
    }()
    
    // BGM淡出时长滑块
    private lazy var bgmFadeOutSlider: UISlider = {
        let slider = UISlider()
        slider.minimumValue = 0
        slider.maximumValue = 5
        slider.value = bgmFadeOutDuration
        slider.addTarget(self, action: #selector(bgmFadeOutChanged(_:)), for: .valueChanged)
        return slider
    }()
    
    // BGM音量标签
    private lazy var bgmVolumeLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.textAlignment = .left
        label.text = "BGM音量: 100%"
        label.font = .systemFont(ofSize: 12)
        return label
    }()
    
    // 视频音量标签
    private lazy var videoVolumeLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.textAlignment = .left
        label.text = "视频音量: 100%"
        label.font = .systemFont(ofSize: 12)
        return label
    }()
    
    // BGM淡入时长标签
    private lazy var bgmFadeInLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.textAlignment = .left
        label.text = "淡入时长: 1.0秒"
        label.font = .systemFont(ofSize: 12)
        return label
    }()
    
    // BGM淡出时长标签
    private lazy var bgmFadeOutLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.textAlignment = .left
        label.text = "淡出时长: 1.0秒"
        label.font = .systemFont(ofSize: 12)
        return label
    }()
    
    // 上传签名相关属性
    private var uploadSignature: String?
    
    // 添加上传签名测试按钮
    private lazy var getSignatureButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("获取上传签名", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .systemPurple
        button.layer.cornerRadius = 8
        button.addTarget(self, action: #selector(getSignatureButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 在VideoEditTestViewController类中添加新的属性
    private var coverImagePath: String?
    private var coverImageView: UIImageView?
    
    // 添加设置封面按钮
    private lazy var setCoverButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("设置封面", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .systemOrange
        button.layer.cornerRadius = 8
        button.addTarget(self, action: #selector(setCoverButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 在属性部分添加上传按钮和上传状态标志
    private var isUploading = false
    private var uploadProgress: UIProgressView?
    private var uploadAlert: UIAlertController?
    private var uploadTimer: Timer?
    // 添加上传按钮定义
    private lazy var uploadButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("上传视频", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .systemIndigo
        button.layer.cornerRadius = 8
        button.addTarget(self, action: #selector(uploadButtonTapped), for: .touchUpInside)
        button.isEnabled = false
        return button
    }()
    
    // 添加上传信息按钮
    private lazy var uploadInfoButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("上传视频信息", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .systemPurple
        button.layer.cornerRadius = 8
        button.addTarget(self, action: #selector(uploadInfoButtonTapped), for: .touchUpInside)
        button.heightAnchor.constraint(equalToConstant: 44).isActive = true
        return button
    }()
    
    // 添加视频发布相关的属性
    private var videoPublish: TXUGCPublish?
    private var publishProgress: UIProgressView?
    private var progressAlert: UIAlertController?
    
    // MARK: - 生命周期
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .black
        setupUI()
        
        if let videoPath = videoPath {
            setupVideoEditor(videoPath: videoPath)
        } else {
            showAlert(title: "错误", message: "视频路径无效")
        }
        
        // 添加设备旋转通知监听
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(orientationChanged),
            name: UIDevice.orientationDidChangeNotification,
            object: nil
        )
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        
        // 停止播放
        videoEditor?.stopPlay()
        
        // 停止定时器
        playbackTimer?.invalidate()
        playbackTimer = nil
        
        // 移除通知监听
        NotificationCenter.default.removeObserver(self)
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // 更新滚动视图的内容大小
        let contentHeight = calculateContentHeight()
        mainScrollView.contentSize = CGSize(width: view.bounds.width, height: contentHeight)
    }
    
    @objc private func orientationChanged() {
        // 设备旋转时更新布局
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.view.setNeedsLayout()
        }
    }
    
    // MARK: - 初始化
    private func setupUI() {
        // 添加主视图
        view.addSubview(previewView)
        view.addSubview(mainScrollView)
        
        // 添加滚动内容视图
        mainScrollView.addSubview(scrollContentView)
        
        // 添加缩略图滚动视图
        scrollContentView.addSubview(thumbnailScrollView)
        
        // 添加裁剪视图
        scrollContentView.addSubview(trimView)
        
        // 添加播放按钮
        previewView.addSubview(playButton)
        
        // 添加时间范围标签
        scrollContentView.addSubview(timeRangeLabel)
        
        // 创建BGM控制面板
        let bgmControlStack = UIStackView()
        bgmControlStack.axis = .vertical
        bgmControlStack.spacing = 10
        bgmControlStack.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        bgmControlStack.layer.cornerRadius = 8
        bgmControlStack.layoutMargins = UIEdgeInsets(top: 10, left: 10, bottom: 10, right: 10)
        
        // BGM测试按钮
        bgmControlStack.addArrangedSubview(bgmTestButton)
        
        // BGM音量控制
        let bgmVolumeStack = UIStackView()
        bgmVolumeStack.axis = .horizontal
        bgmVolumeStack.spacing = 10
        bgmVolumeStack.addArrangedSubview(bgmVolumeLabel)
        bgmVolumeStack.addArrangedSubview(bgmVolumeSlider)
        bgmControlStack.addArrangedSubview(bgmVolumeStack)
        
        // 视频音量控制
        let videoVolumeStack = UIStackView()
        videoVolumeStack.axis = .horizontal
        videoVolumeStack.spacing = 10
        videoVolumeStack.addArrangedSubview(videoVolumeLabel)
        videoVolumeStack.addArrangedSubview(videoVolumeSlider)
        bgmControlStack.addArrangedSubview(videoVolumeStack)
        
        // BGM淡入时长控制
        let bgmFadeInStack = UIStackView()
        bgmFadeInStack.axis = .horizontal
        bgmFadeInStack.spacing = 10
        bgmFadeInStack.addArrangedSubview(bgmFadeInLabel)
        bgmFadeInStack.addArrangedSubview(bgmFadeInSlider)
        bgmControlStack.addArrangedSubview(bgmFadeInStack)
        
        // BGM淡出时长控制
        let bgmFadeOutStack = UIStackView()
        bgmFadeOutStack.axis = .horizontal
        bgmFadeOutStack.spacing = 10
        bgmFadeOutStack.addArrangedSubview(bgmFadeOutLabel)
        bgmFadeOutStack.addArrangedSubview(bgmFadeOutSlider)
        bgmControlStack.addArrangedSubview(bgmFadeOutStack)
        
        scrollContentView.addSubview(bgmControlStack)
        
        // 创建旋转控制面板
        let rotationControlStack = UIStackView()
        rotationControlStack.axis = .vertical
        rotationControlStack.spacing = 10
        rotationControlStack.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        rotationControlStack.layer.cornerRadius = 8
        rotationControlStack.layoutMargins = UIEdgeInsets(top: 10, left: 10, bottom: 10, right: 10)
        
        // 添加旋转标题
        let rotationTitle = UILabel()
        rotationTitle.text = "视频旋转"
        rotationTitle.textColor = .white
        rotationTitle.font = .systemFont(ofSize: 14, weight: .medium)
        rotationControlStack.addArrangedSubview(rotationTitle)
        
        // 创建旋转按钮行
        let rotationButtonStack = UIStackView()
        rotationButtonStack.axis = .horizontal
        rotationButtonStack.spacing = 10
        rotationButtonStack.distribution = .fillEqually
        
        // 创建4个旋转按钮
        let rotationValues: [Int32] = [0, 90, 180, 270]
        let rotationTitles = ["0°", "90°", "180°", "270°"]
        
        for (index, value) in rotationValues.enumerated() {
            let button = UIButton(type: .system)
            button.setTitle(rotationTitles[index], for: .normal)
            button.setTitleColor(.white, for: .normal)
            button.backgroundColor = .darkGray
            button.layer.cornerRadius = 8
            button.tag = Int(value)
            button.addTarget(self, action: #selector(rotationButtonTapped(_:)), for: .touchUpInside)
            rotationButtonStack.addArrangedSubview(button)
            rotationButtons.append(button)
        }
        
        rotationControlStack.addArrangedSubview(rotationButtonStack)
        scrollContentView.addSubview(rotationControlStack)
        
        // 创建按钮堆栈
        // 改为垂直布局的主堆栈，内含两个水平堆栈
        let buttonStack = UIStackView()
        buttonStack.axis = .vertical
        buttonStack.spacing = 10
        
        // 第一行按钮
        let topButtonStack = UIStackView(arrangedSubviews: [backButton, generateButton, saveButton, getSignatureButton])
        topButtonStack.axis = .horizontal
        topButtonStack.spacing = 10
        topButtonStack.distribution = .fillEqually
        topButtonStack.heightAnchor.constraint(equalToConstant: 44).isActive = true
        
        // 第二行按钮
        let bottomButtonStack = UIStackView(arrangedSubviews: [setCoverButton, uploadButton, uploadInfoButton])
        bottomButtonStack.axis = .horizontal
        bottomButtonStack.spacing = 10
        bottomButtonStack.distribution = .fillEqually
        bottomButtonStack.heightAnchor.constraint(equalToConstant: 44).isActive = true
        
        // 添加两行按钮到主堆栈
        buttonStack.addArrangedSubview(topButtonStack)
        buttonStack.addArrangedSubview(bottomButtonStack)
        
        scrollContentView.addSubview(buttonStack)
        
        scrollContentView.addSubview(progressView)
        scrollContentView.addSubview(progressLabel)
        
        // 设置约束
        previewView.translatesAutoresizingMaskIntoConstraints = false
        mainScrollView.translatesAutoresizingMaskIntoConstraints = false
        scrollContentView.translatesAutoresizingMaskIntoConstraints = false
        thumbnailScrollView.translatesAutoresizingMaskIntoConstraints = false
        trimView.translatesAutoresizingMaskIntoConstraints = false
        playButton.translatesAutoresizingMaskIntoConstraints = false
        timeRangeLabel.translatesAutoresizingMaskIntoConstraints = false
        buttonStack.translatesAutoresizingMaskIntoConstraints = false
        progressView.translatesAutoresizingMaskIntoConstraints = false
        progressLabel.translatesAutoresizingMaskIntoConstraints = false
        bgmControlStack.translatesAutoresizingMaskIntoConstraints = false
        rotationControlStack.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            // 预览视图
            previewView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            previewView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            previewView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            previewView.heightAnchor.constraint(equalTo: view.heightAnchor, multiplier: 0.5),
            
            // 主滚动视图
            mainScrollView.topAnchor.constraint(equalTo: previewView.bottomAnchor),
            mainScrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            mainScrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            mainScrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            // 滚动内容视图
            scrollContentView.topAnchor.constraint(equalTo: mainScrollView.topAnchor),
            scrollContentView.leadingAnchor.constraint(equalTo: mainScrollView.leadingAnchor),
            scrollContentView.trailingAnchor.constraint(equalTo: mainScrollView.trailingAnchor),
            scrollContentView.bottomAnchor.constraint(equalTo: mainScrollView.bottomAnchor),
            scrollContentView.widthAnchor.constraint(equalTo: mainScrollView.widthAnchor),
            
            // 播放按钮
            playButton.centerXAnchor.constraint(equalTo: previewView.centerXAnchor),
            playButton.centerYAnchor.constraint(equalTo: previewView.centerYAnchor),
            playButton.widthAnchor.constraint(equalToConstant: 50),
            playButton.heightAnchor.constraint(equalToConstant: 50),
            
            // 缩略图滚动视图
            thumbnailScrollView.topAnchor.constraint(equalTo: scrollContentView.topAnchor, constant: 20),
            thumbnailScrollView.leadingAnchor.constraint(equalTo: scrollContentView.leadingAnchor, constant: 20),
            thumbnailScrollView.trailingAnchor.constraint(equalTo: scrollContentView.trailingAnchor, constant: -20),
            thumbnailScrollView.heightAnchor.constraint(equalToConstant: 50),
            
            // 裁剪视图
            trimView.topAnchor.constraint(equalTo: thumbnailScrollView.topAnchor),
            trimView.leadingAnchor.constraint(equalTo: thumbnailScrollView.leadingAnchor),
            trimView.trailingAnchor.constraint(equalTo: thumbnailScrollView.trailingAnchor),
            trimView.heightAnchor.constraint(equalTo: thumbnailScrollView.heightAnchor),
            
            // 时间范围标签
            timeRangeLabel.topAnchor.constraint(equalTo: thumbnailScrollView.bottomAnchor, constant: 10),
            timeRangeLabel.centerXAnchor.constraint(equalTo: scrollContentView.centerXAnchor),
            
            // BGM控制面板
            bgmControlStack.topAnchor.constraint(equalTo: timeRangeLabel.bottomAnchor, constant: 20),
            bgmControlStack.leadingAnchor.constraint(equalTo: scrollContentView.leadingAnchor, constant: 20),
            bgmControlStack.trailingAnchor.constraint(equalTo: scrollContentView.trailingAnchor, constant: -110),
            
            // 旋转控制面板
            rotationControlStack.topAnchor.constraint(equalTo: bgmControlStack.bottomAnchor, constant: 20),
            rotationControlStack.leadingAnchor.constraint(equalTo: scrollContentView.leadingAnchor, constant: 20),
            rotationControlStack.trailingAnchor.constraint(equalTo: scrollContentView.trailingAnchor, constant: -20),
            
            // 按钮堆栈
            buttonStack.leadingAnchor.constraint(equalTo: scrollContentView.leadingAnchor, constant: 20),
            buttonStack.trailingAnchor.constraint(equalTo: scrollContentView.trailingAnchor, constant: -20),
            buttonStack.topAnchor.constraint(equalTo: rotationControlStack.bottomAnchor, constant: 20),
            
            // 进度条
            progressView.leadingAnchor.constraint(equalTo: scrollContentView.leadingAnchor, constant: 20),
            progressView.trailingAnchor.constraint(equalTo: scrollContentView.trailingAnchor, constant: -20),
            progressView.topAnchor.constraint(equalTo: buttonStack.bottomAnchor, constant: 20),
            
            // 进度标签
            progressLabel.centerXAnchor.constraint(equalTo: progressView.centerXAnchor),
            progressLabel.topAnchor.constraint(equalTo: progressView.bottomAnchor, constant: 5),
            progressLabel.bottomAnchor.constraint(equalTo: scrollContentView.bottomAnchor, constant: -20)
        ])
        
        // 设置滚动视图的内容大小
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            // 计算内容高度
            let contentHeight = self.calculateContentHeight()
            self.mainScrollView.contentSize = CGSize(width: self.view.bounds.width, height: contentHeight)
        }
        
        // 初始化旋转按钮状态
        updateRotationButtonStates()
    }
    
    // 计算滚动内容的高度
    private func calculateContentHeight() -> CGFloat {
        // 基础高度：顶部间距 + 缩略图高度 + 时间标签高度 + BGM控制面板高度 + 旋转控制面板高度 + 按钮堆栈高度 + 进度条高度 + 进度标签高度 + 底部间距
        let baseHeight: CGFloat = 20 + 50 + 20 + 120 + 20 + 20 + 20 + 100 + 20 + 98 + 20 + 30 + 20
        
        // 根据设备类型调整高度
        let screenHeight = UIScreen.main.bounds.height
        let minHeight = screenHeight * 0.5 // 至少占屏幕高度的一半
        
        return max(baseHeight, minHeight)
    }
    
    private func setupVideoEditor(videoPath: String) {
        // 1. 初始化视频信息
        let videoInfo = TXVideoInfoReader.getVideoInfo(videoPath)
        videoDuration = CGFloat(videoInfo!.duration)
        startTime = 0
        endTime = videoDuration
        
        // 更新时间范围显示
        updateTimeRangeLabel()
        
        // 2. 初始化视频编辑器
        let param = TXPreviewParam()
        param.videoView = previewView
        param.renderMode = .PREVIEW_RENDER_MODE_FILL_EDGE
        
        videoEditor = TXVideoEditer(preview: param)
        videoEditor?.setVideoPath(videoPath)
        
        // 设置视频旋转角度
        videoEditor?.setRenderRotation(currentRotation)
        
        videoEditor?.preview(atTime: 0)
        
        // 3. 设置视频生成回调
        videoEditor?.generateDelegate = self
        
        // 4. 获取视频缩略图
        loadVideoThumbnails(videoPath: videoPath, videoInfo: videoInfo!)
    }
    
    private func loadVideoThumbnails(videoPath: String, videoInfo: TXVideoInfo) {
        // 计算需要的缩略图数量（每秒1张）
        let count = max(10, Int(ceil(videoInfo.duration)))
        
        // 获取缩略图
        TXVideoInfoReader.getSampleImages(Int32(count), 
                                        videoPath: videoPath) { (number, image) -> Bool in
            if let image = image {
                self.thumbnailArray.append(image)
                
                // 如果获取了所有缩略图，更新UI
                if number == count {
                    DispatchQueue.main.async {
                        self.updateThumbnailScrollView()
                    }
                }
            }
            
            // 返回 true 继续获取下一张，返回 false 停止获取
            return true 
        }
    }
    
    private func updateThumbnailScrollView() {
        // 清除旧内容
        for subview in thumbnailScrollView.subviews {
            subview.removeFromSuperview()
        }
        
        // 计算总宽度
        let totalWidth = thumbnailScrollView.frame.width
        let thumbHeight = thumbnailScrollView.frame.height
        
        // 创建缩略图容器
        let containerView = UIView(frame: CGRect(x: 0, y: 0, width: totalWidth, height: thumbHeight))
        thumbnailScrollView.addSubview(containerView)
        
        // 计算每个缩略图的宽度
        let thumbCount = thumbnailArray.count
        let thumbWidth = totalWidth / CGFloat(thumbCount)
        
        // 添加缩略图
        for (index, thumbnail) in thumbnailArray.enumerated() {
            let imageView = UIImageView(frame: CGRect(
                x: CGFloat(index) * thumbWidth,
                y: 0,
                width: thumbWidth,
                height: thumbHeight
            ))
            imageView.image = thumbnail
            imageView.contentMode = .scaleAspectFill
            imageView.clipsToBounds = true
            containerView.addSubview(imageView)
        }
        
        // 设置内容大小
        thumbnailScrollView.contentSize = CGSize(width: totalWidth, height: thumbHeight)
        
        // 更新裁剪视图
        trimView.setupTrimmer(videoDuration: videoDuration)
    }
    
    private func updateTimeRangeLabel() {
        timeRangeLabel.text = String(format: "%02d:%02d - %02d:%02d",
                                     Int(startTime) / 60, Int(startTime) % 60,
                                     Int(endTime) / 60, Int(endTime) % 60)
    }
    
    // 修改 playButtonTapped 方法
    @objc private func playButtonTapped() {
        if isPlaying {
            // 暂停播放
            videoEditor?.pausePlay()
            playButton.setImage(UIImage(systemName: "play.fill"), for: .normal)
            
            // 停止播放进度更新定时器
            playbackTimer?.invalidate()
            playbackTimer = nil
            
            // 注意：我们不需要重置 lastPlaybackTime，以便从当前位置继续播放
        } else {
            // 如果是播放结束后再次点击播放，则从头开始播放
            if lastPlaybackTime >= endTime {
                lastPlaybackTime = startTime
            }
            
            // 从当前位置播放裁剪区域
            videoEditor?.startPlay(fromTime: lastPlaybackTime, toTime: endTime)
            playButton.setImage(UIImage(systemName: "pause.fill"), for: .normal)
            
            // 创建定时器来更新播放进度
            playbackTimer = Timer.scheduledTimer(withTimeInterval: 0.05, repeats: true) { [weak self] _ in
                guard let self = self else { return }
                
                // 更新播放时间
                self.lastPlaybackTime += 0.05
                
                // 如果到达结束时间，暂停播放
                if self.lastPlaybackTime >= self.endTime {
                    // 暂停播放
                    self.videoEditor?.pausePlay()
                    DispatchQueue.main.async {
                        self.playButton.setImage(UIImage(systemName: "play.fill"), for: .normal)
                    }
                    self.isPlaying = false
                    
                    // 停止定时器
                    self.playbackTimer?.invalidate()
                    self.playbackTimer = nil
                    
                    // 保持预览指示器在结束位置
                    self.updatePreviewIndicatorPosition(at: self.endTime)
                    
                    // 不要重置 lastPlaybackTime，这样可以记住结束位置
                    return
                }
                    
                    // 更新预览指示器位置
                    self.updatePreviewIndicatorPosition(at: self.lastPlaybackTime)
                }
        }
        isPlaying = !isPlaying
    }

    // 添加新方法用于根据播放时间更新预览指示器位置
    private func updatePreviewIndicatorPosition(at time: CGFloat) {
        // 计算相对进度
        let progress = (time - startTime) / (endTime - startTime)
        
        // 更新裁剪视图中的预览指示器位置
        trimView.updatePreviewIndicatorForPlayback(progress: progress)
    }
    
    @objc private func generateButtonTapped() {
        guard !isGenerating else { return }
        
        isGenerating = true
        generateButton.isEnabled = false
        progressView.isHidden = false
        progressLabel.isHidden = false
        progressView.progress = 0
        progressLabel.text = "0%"
        
        // 设置裁剪区域
        videoEditor?.setCutFromTime(Float(startTime), toTime: Float(endTime))
        
        // 确保应用旋转角度设置
        videoEditor?.setRenderRotation(currentRotation)
        
        // 生成视频路径
        let outputPath = NSTemporaryDirectory().appending("edited_video_\(Int(Date().timeIntervalSince1970)).mp4")
        generatedVideoPath = outputPath
        
        // 开始生成视频
        videoEditor?.generateVideo(.VIDEO_COMPRESSED_720P, videoOutputPath: outputPath)
        
        // 如果有封面图，打印日志（实际上传时会用到）
        if let coverPath = coverImagePath {
            print("视频生成时使用封面路径: \(coverPath)")
        }
    }
    
    @objc private func backButtonTapped() {
        if isGenerating {
            let alert = UIAlertController(title: "警告", message: "视频正在生成中，确定要取消吗？", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "取消", style: .cancel))
            alert.addAction(UIAlertAction(title: "确定", style: .destructive) { [weak self] _ in
                self?.dismiss(animated: true)
            })
            present(alert, animated: true)
        } else {
            dismiss(animated: true)
        }
    }
    
    @objc private func saveButtonTapped() {
        guard let outputPath = generatedVideoPath, FileManager.default.fileExists(atPath: outputPath) else {
            showAlert(title: "错误", message: "视频文件不存在")
            return
        }
        
        // 请求相册访问权限
        PHPhotoLibrary.requestAuthorization { [weak self] status in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                switch status {
                case .authorized, .limited:
                    self.saveVideoToPhotoLibrary(videoPath: outputPath)
                case .denied, .restricted:
                    self.showAlert(title: "无法访问相册", message: "请在设置中允许应用访问您的相册")
                case .notDetermined:
                    // 理论上不会走到这里，因为我们已经请求了权限
                    break
                @unknown default:
                    break
                }
            }
        }
    }
    
    private func saveVideoToPhotoLibrary(videoPath: String) {
        let videoURL = URL(fileURLWithPath: videoPath)
        
        PHPhotoLibrary.shared().performChanges({
            PHAssetChangeRequest.creationRequestForAssetFromVideo(atFileURL: videoURL)
        }) { [weak self] success, error in
            DispatchQueue.main.async {
                if success {
                    self?.showAlert(title: "成功", message: "视频已保存到相册")
                } else {
                    let errorMessage = error?.localizedDescription ?? "未知错误"
                    self?.showAlert(title: "保存失败", message: errorMessage)
                }
            }
        }
    }
    
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    // MARK: - TXVideoGenerateListener
    func onGenerateProgress(_ progress: Float) {
        DispatchQueue.main.async { [weak self] in
            self?.progressView.progress = progress
            self?.progressLabel.text = "\(Int(progress * 100))%"
        }
    }
    
    func onGenerateComplete(_ result: TXGenerateResult!) {
        isGenerating = false
        
        DispatchQueue.main.async { [weak self] in
            self?.generateButton.isEnabled = true
            
            if result.retCode == .GENERATE_RESULT_OK {
                // 生成成功
                self?.saveButton.isEnabled = true
                self?.uploadButton.isEnabled = true
                self?.showAlert(title: "生成成功", message: "视频剪辑完成，可以保存到相册或上传")
            } else {
                // 生成失败
                self?.showAlert(title: "生成失败", message: result.descMsg)
            }
        }
    }
    
    // MARK: - BGM相关方法
    @objc private func testBgmButtonTapped() {
        // 获取测试BGM文件路径
        if let bundlePath = Bundle.main.path(forResource: "ap001", ofType: "mp3") {
            bgmPath = bundlePath
            
            // 设置BGM
            videoEditor?.setBGM(bundlePath) { [weak self] result in
                guard let self = self else { return }
                
                DispatchQueue.main.async {
                    if result == 0 {
                        // 设置BGM成功
                        self.videoEditor?.setBGMVolume(self.bgmVolume)
                        self.videoEditor?.setVideoVolume(self.videoVolume)
                        self.videoEditor?.setBGMLoop(true)  // 循环播放
                        self.videoEditor?.setBGMFadeInDuration(self.bgmFadeInDuration, fadeOutDuration: self.bgmFadeOutDuration)  // 设置淡入淡出效果
                        
                        // 更新UI状态
                        self.bgmTestButton.backgroundColor = .systemGreen
                        self.bgmTestButton.setTitle("BGM已添加", for: .normal)
                        
                        // 显示成功提示
                        self.showAlert(title: "成功", message: "BGM添加成功")
                    } else {
                        // 设置BGM失败
                        self.showAlert(title: "错误", message: "BGM添加失败")
                    }
                }
            }
        } else {
            showAlert(title: "错误", message: "未找到测试BGM文件")
        }
    }
    
    @objc private func bgmVolumeChanged(_ sender: UISlider) {
        bgmVolume = sender.value
        videoEditor?.setBGMVolume(bgmVolume)
        bgmVolumeLabel.text = "BGM音量: \(Int(bgmVolume * 100))%"
    }
    
    @objc private func videoVolumeChanged(_ sender: UISlider) {
        videoVolume = sender.value
        videoEditor?.setVideoVolume(videoVolume)
        videoVolumeLabel.text = "视频音量: \(Int(videoVolume * 100))%"
    }
    
    // BGM淡入时长变化事件
    @objc private func bgmFadeInChanged(_ sender: UISlider) {
        bgmFadeInDuration = sender.value
        bgmFadeInLabel.text = "淡入时长: \(String(format: "%.1f", bgmFadeInDuration))秒"
        
        // 更新BGM淡入淡出设置
        videoEditor?.setBGMFadeInDuration(bgmFadeInDuration, fadeOutDuration: bgmFadeOutDuration)
    }
    
    // BGM淡出时长变化事件
    @objc private func bgmFadeOutChanged(_ sender: UISlider) {
        bgmFadeOutDuration = sender.value
        bgmFadeOutLabel.text = "淡出时长: \(String(format: "%.1f", bgmFadeOutDuration))秒"
        
        // 更新BGM淡入淡出设置
        videoEditor?.setBGMFadeInDuration(bgmFadeInDuration, fadeOutDuration: bgmFadeOutDuration)
    }
    
    // MARK: - 旋转按钮点击事件
    @objc private func rotationButtonTapped(_ sender: UIButton) {
        let rotation = Int32(sender.tag)
        currentRotation = rotation
        
        // 更新视频旋转
        videoEditor?.setRenderRotation(rotation)
        
        // 更新按钮状态
        updateRotationButtonStates()
    }
    
    // 更新旋转按钮状态
    private func updateRotationButtonStates() {
        for button in rotationButtons {
            if Int32(button.tag) == currentRotation {
                button.backgroundColor = .systemBlue
            } else {
                button.backgroundColor = .darkGray
            }
        }
    }
    
    // 修改getSignatureButtonTapped方法，成功获取签名后启用上传按钮
    @objc private func getSignatureButtonTapped() {
        // 显示加载状态
        getSignatureButton.isEnabled = false
        getSignatureButton.setTitle("获取中...", for: .normal)
        
        // 调用API获取上传签名
        APIManager.shared.getVodSignature { [weak self] result in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                // 恢复按钮状态
                self.getSignatureButton.isEnabled = true
                self.getSignatureButton.setTitle("获取上传签名", for: .normal)
                
                switch result {
                case .success(let response):
                    if response.status == 200, let signData = response.data {
                        // 保存签名
                        self.uploadSignature = signData.sign
                        
                        // 打印签名
                        print("获取到上传签名: \(signData.sign)")
                        
                        // 启用上传按钮 (如果视频已生成)
                        if self.generatedVideoPath != nil {
                            self.uploadButton.isEnabled = true
                        }
                        
                        // 显示成功提示
                        self.showAlert(title: "成功", message: "成功获取上传签名")
                        
                        // 更新按钮状态
                        self.getSignatureButton.backgroundColor = .systemGreen
                        self.getSignatureButton.setTitle("已获取签名", for: .normal)
                    } else {
                        // 显示错误信息
                        self.showAlert(title: "错误", message: response.errMsg.isEmpty ? "获取签名失败" : response.errMsg)
                    }
                    
                case .failure(let error):
                    // 显示错误信息
                    self.showAlert(title: "错误", message: error.errorMessage)
                }
            }
        }
    }
    
    // MARK: - 设置封面相关方法
    // 添加设置封面按钮点击事件
    @objc private func setCoverButtonTapped() {
        // 创建图片选择器
        let imagePicker = UIImagePickerController()
        imagePicker.delegate = self
        imagePicker.sourceType = .photoLibrary
        imagePicker.allowsEditing = false
        
        // 显示图片选择器
        present(imagePicker, animated: true)
    }
    
    // 保存图片到临时目录
    private func saveImageToTemp(image: UIImage) -> String? {
        let tempDirURL = URL(fileURLWithPath: NSTemporaryDirectory(), isDirectory: true)
        let imageName = "cover_\(Int(Date().timeIntervalSince1970)).jpg"
        let fileURL = tempDirURL.appendingPathComponent(imageName)
        
        // 将图片转换为JPEG数据
        if let imageData = image.jpegData(compressionQuality: 0.85) {
            do {
                try imageData.write(to: fileURL)
                return fileURL.path
            } catch {
                print("保存封面图片失败: \(error)")
                return nil
            }
        }
        
        return nil
    }
    
    // 显示选中的封面图片
    private func showSelectedCoverImage(image: UIImage) {
        // 如果之前没有创建封面图预览，创建一个
        if coverImageView == nil {
            let imageView = UIImageView()
            imageView.contentMode = .scaleAspectFill
            imageView.clipsToBounds = true
            imageView.layer.cornerRadius = 8
            imageView.layer.borderColor = UIColor.white.cgColor
            imageView.layer.borderWidth = 2
            
            scrollContentView.addSubview(imageView)
            
            // 设置约束
            imageView.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                imageView.topAnchor.constraint(equalTo: timeRangeLabel.bottomAnchor, constant: 20),
                imageView.trailingAnchor.constraint(equalTo: scrollContentView.trailingAnchor, constant: -20),
                imageView.widthAnchor.constraint(equalToConstant: 80),
                imageView.heightAnchor.constraint(equalToConstant: 80)
            ])
            
            self.coverImageView = imageView
        }
        
        // 更新图片
        self.coverImageView?.image = image
        
        // 更新设置封面按钮状态
        setCoverButton.backgroundColor = .systemGreen
        setCoverButton.setTitle("已设置封面", for: .normal)
    }
    
    // 处理图片选择结果
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        // 获取选中的图片
        if let selectedImage = info[.originalImage] as? UIImage {
            // 保存图片到临时目录
            if let imagePath = saveImageToTemp(image: selectedImage) {
                // 保存封面路径
                self.coverImagePath = imagePath
                
                // 显示选中的图片
                showSelectedCoverImage(image: selectedImage)
                
                // 打印封面路径
                print("选择的封面路径: \(imagePath)")
            }
        }
        
        // 关闭图片选择器
        picker.dismiss(animated: true)
    }
    
    // 取消选择
    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true)
    }
    
    // MARK: - 视频上传相关方法
    @objc private func uploadButtonTapped() {
        guard !isUploading else { return }
        
        // 检查是否已生成视频
        guard let videoPath = generatedVideoPath, FileManager.default.fileExists(atPath: videoPath) else {
            showAlert(title: "错误", message: "请先生成视频")
            return
        }
        
        // 检查是否已获取签名
        guard let signature = uploadSignature else {
            showAlert(title: "错误", message: "请先获取上传签名")
            return
        }
        
        // 显示上传中状态
        isUploading = true
        uploadButton.isEnabled = false
        uploadButton.setTitle("上传中...", for: .normal)
        
        // 创建进度对话框
        uploadAlert = UIAlertController(title: "上传中", message: "正在上传视频...", preferredStyle: .alert)
        
        // 创建进度条
        let progressView = UIProgressView(progressViewStyle: .default)
        progressView.progress = 0
        progressView.frame = CGRect(x: 10, y: 70, width: 250, height: 2)
        uploadProgress = progressView
        
        // 添加进度条到对话框
        uploadAlert?.view.addSubview(progressView)
        
        // 添加取消按钮
        let cancelAction = UIAlertAction(title: "取消", style: .cancel) { [weak self] _ in
            // 取消上传
            self?.cancelUpload()
        }
        uploadAlert?.addAction(cancelAction)
        
        present(uploadAlert!, animated: true)
        
        // 打印上传参数
        print("视频上传参数:")
        print("- 签名: \(signature)")
        print("- 视频路径: \(videoPath)")
        if let coverPath = coverImagePath {
            print("- 封面路径: \(coverPath)")
        } else {
            print("- 封面路径: nil")
        }
        print("- HTTPS: 启用")
        
        // 模拟上传过程
        var progress: Float = 0
        
        // 定义定时器回调
        let timerCallback: (Timer) -> Void = { [weak self] timer in
            guard let self = self else {
                timer.invalidate()
                return
            }
            
            // 增加进度
            progress += 0.05
            let cappedProgress = min(progress, 1.0)
            
            // 更新进度条和消息
            self.uploadProgress?.progress = cappedProgress
            let percentComplete = Int(cappedProgress * 100)
            let message = "正在上传视频... \(percentComplete)%"
            self.uploadAlert?.message = message
            
            // 检查是否完成
            if progress >= 1.0 {
                // 停止定时器
                timer.invalidate()
                self.uploadTimer = nil
                
                // 延迟执行完成操作
                self.finishUploadAfterDelay()
            }
        }
        
        // 创建定时器
        uploadTimer = Timer.scheduledTimer(withTimeInterval: 0.5, 
                                          repeats: true, 
                                          block: timerCallback)
        
        // 将定时器添加到当前运行循环
        if let timer = uploadTimer {
            RunLoop.current.add(timer, forMode: .common)
        }
    }
    
    // 添加辅助方法完成上传
    private func finishUploadAfterDelay() {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            guard let self = self else { return }
            
            self.uploadAlert?.dismiss(animated: true) {
                // 模拟上传结果
                self.handleUploadComplete(isSuccess: true)
            }
        }
    }
    
    // 添加取消上传方法
    private func cancelUpload() {
        uploadTimer?.invalidate()
        uploadTimer = nil
        
        isUploading = false
        uploadButton.isEnabled = true
        uploadButton.setTitle("上传视频", for: .normal)
        
        print("用户取消了上传")
    }
    
    // 添加处理上传完成的方法
    private func handleUploadComplete(isSuccess: Bool) {
        if isSuccess {
            // 模拟成功结果
            let timestamp = Int(Date().timeIntervalSince1970)
            let videoId = "v\(timestamp)"
            
            // 构建URL
            let baseURL = "https://1302866819.vod2.myqcloud.com/"
            let videoURL = baseURL + videoId + ".mp4"
            
            // 处理封面URL
            let coverURL: String
            if coverImagePath != nil {
                coverURL = baseURL + videoId + ".jpg"
            } else {
                coverURL = "无"
            }
            
            // 打印上传结果
            print("视频上传成功:")
            print("- 视频ID: \(videoId)")
            print("- 视频播放地址: \(videoURL)")
            print("- 封面地址: \(coverURL)")
            
            // 构建消息文本
            let messageText = "视频ID: \(videoId)\n播放地址: \(videoURL)"
            
            // 创建成功对话框
            let successAlert = UIAlertController(
                title: "上传成功",
                message: messageText,
                preferredStyle: .alert
            )
            
            // 添加确定按钮
            let confirmAction = UIAlertAction(title: "确定", style: .default)
            successAlert.addAction(confirmAction)
            
            // 显示对话框
            present(successAlert, animated: true)
            
            // 更新上传按钮状态
            updateUploadButtonForSuccess()
        } else {
            // 模拟失败结果
            let errorCode = -1001
            let errorMsg = "网络连接失败"
            
            // 构建消息文本
            let messageText = "错误码: \(errorCode)\n错误信息: \(errorMsg)"
            
            // 创建失败对话框
            let errorAlert = UIAlertController(
                title: "上传失败",
                message: messageText,
                preferredStyle: .alert
            )
            
            // 添加确定按钮
            let confirmAction = UIAlertAction(title: "确定", style: .default)
            errorAlert.addAction(confirmAction)
            
            // 显示对话框
            present(errorAlert, animated: true)
            
            // 恢复上传按钮状态
            updateUploadButtonForFailure()
        }
    }
    
    // 更新按钮为成功状态
    private func updateUploadButtonForSuccess() {
        isUploading = false
        uploadButton.isEnabled = true
        uploadButton.setTitle("上传成功", for: .normal)
        uploadButton.backgroundColor = .systemGreen
    }
    
    // 更新按钮为失败状态
    private func updateUploadButtonForFailure() {
        isUploading = false
        uploadButton.isEnabled = true
        uploadButton.setTitle("上传失败", for: .normal)
        uploadButton.backgroundColor = .systemRed
    }
    
    // 添加上传视频信息的方法
    @objc private func uploadInfoButtonTapped() {
//        // 检查是否有录制的视频
//        guard let videoPath = generatedVideoPath else {
//            showAlert(title: "错误", message: "请先录制视频")
//            return
//        }
//        
//        // 检查文件是否存在
//        if !FileManager.default.fileExists(atPath: videoPath) {
//            showAlert(title: "错误", message: "视频文件不存在")
//            return
//        }
//        
//        // 获取视频信息
//        let videoURL = URL(fileURLWithPath: videoPath)
//        
//        // 获取文件大小
//        var fileSize: Int = 0
//        do {
//            let attributes = try FileManager.default.attributesOfItem(atPath: videoPath)
//            if let size = attributes[.size] as? Int {
//                fileSize = size
//            }
//        } catch {
//            print("获取文件大小错误: \(error)")
//        }
//        
//        // 打开选项对话框
//        let alert = UIAlertController(
//            title: "上传视频信息",
//            message: "请选择上传方式",
//            preferredStyle: .actionSheet
//        )
//        
//        // 创建使用假数据上传的操作
//        let fakeDataAction = UIAlertAction(
//            title: "使用假数据上传",
//            style: .default
//        ) { [weak self] _ in
//            self?.uploadVideoInfoWithFakeData(videoSize: fileSize)
//        }
//        
//        // 创建填写信息上传的操作
//        let formAction = UIAlertAction(
//            title: "填写信息上传",
//            style: .default
//        ) { [weak self] _ in
//            self?.showVideoInfoForm(videoSize: fileSize)
//        }
//        
//        // 取消选项
//        let cancelAction = UIAlertAction(title: "取消", style: .cancel)
//        
//        // 添加操作到对话框
//        alert.addAction(fakeDataAction)
//        alert.addAction(formAction)
//        alert.addAction(cancelAction)
//        
//        // 显示对话框
//        present(alert, animated: true)
    }

    // 显示视频信息表单
//    private func showVideoInfoForm(videoSize: Int) {
//        // 获取当前时间戳
//        let timestamp = Int(Date().timeIntervalSince1970)
//        let defaultTitle = "测试视频_\(timestamp)"
//        let defaultVideoId = "v\(timestamp)"
//        let defaultCoverImg = "https://1302866819.vod2.myqcloud.com/cover_\(timestamp).jpg"
//        
//        // 创建表单对话框
//        let formAlert = UIAlertController(
//            title: "视频信息", 
//            message: "请填写以下信息", 
//            preferredStyle: .alert
//        )
//        
//        // 添加标题输入框
//        formAlert.addTextField { textField in
//            textField.placeholder = "视频标题"
//            textField.text = defaultTitle
//        }
//        
//        // 添加视频ID输入框
//        formAlert.addTextField { textField in
//            textField.placeholder = "视频ID"
//            textField.text = defaultVideoId
//        }
//        
//        // 添加封面URL输入框
//        formAlert.addTextField { textField in
//            textField.placeholder = "封面URL"
//            textField.text = defaultCoverImg
//        }
//        
//        // 添加描述输入框
//        formAlert.addTextField { textField in
//            textField.placeholder = "视频描述（可选）"
//        }
//        
//        // 添加取消按钮
//        let cancelAction = UIAlertAction(title: "取消", style: .cancel)
//        formAlert.addAction(cancelAction)
//        
//        // 添加提交按钮
//        let submitAction = UIAlertAction(
//            title: "提交", 
//            style: .default
//        ) { [weak self, weak formAlert] _ in
//            guard let self = self, let formAlert = formAlert else { return }
//            
//            // 获取输入的值
//            let textFields = formAlert.textFields
//            
//            // 分别获取各个值
//            let title = textFields?[0].text ?? defaultTitle
//            let videoId = textFields?[1].text ?? defaultVideoId
//            let coverImg = textFields?[2].text ?? defaultCoverImg
//            
//            // 获取描述（可能为空）
//            let description = textFields?[3].text ?? ""
//            let worksDescribe: String? = description.isEmpty ? nil : description
//            
//            // 创建请求
//            var request = ShortVideoWorksAddRequest()
//            request.videoId = videoId
//            request.worksCoverImg = coverImg
//            request.worksTitle = title
//            request.worksDescribe = worksDescribe
//            request.duration = 15  // 假设15秒
//            request.size = videoSize
//            
//            // 提交请求
////            self.submitVideoInfo(request: request)
//        }
//        formAlert.addAction(submitAction)
//        
//        // 显示表单
//        present(formAlert, animated: true)
//    }
    
    // 使用假数据上传视频信息
//    private func uploadVideoInfoWithFakeData(videoSize: Int) {
//        // 创建加载中提示
//        let loadingAlert = UIAlertController(title: "正在上传", message: "请稍候...", preferredStyle: .alert)
//        present(loadingAlert, animated: true)
//        
//        // 获取当前时间戳，用于生成唯一ID
//        let timestamp = Int(Date().timeIntervalSince1970)
//        
//        // 创建假数据请求模型
//        var request = ShortVideoWorksAddRequest()
//        
//        // 视频ID
//        let videoId = "v\(timestamp)"
//        request.videoId = videoId
//        
//        // 封面图片URL
//        let coverImgURL = "https://1302866819.vod2.myqcloud.com/cover_\(timestamp).jpg"
//        request.worksCoverImg = coverImgURL
//        
//        // 视频标题
//        let title = "测试视频_\(timestamp)"
//        request.worksTitle = title
//        
//        // 设置视频时长和大小
//        request.duration = 15  // 假设15秒
//        request.size = videoSize
//        
//        // 调用API上传视频信息
//        APIManager.shared.uploadShortVideoWorks(params: request) { [weak self] result in
//            DispatchQueue.main.async { [weak loadingAlert] in
//                // 获取加载对话框的引用
//                guard let loadingAlert = loadingAlert else { return }
//                
//                // 关闭加载提示
//                loadingAlert.dismiss(animated: true) {
//                    guard let self = self else { return }
//                    
//                    // 处理结果
//                    switch result {
//                    case .success(let response):
//                        if response.status == 200 {
//                            // 获取作品ID
//                            let worksId = response.data?.worksId ?? 0
//                            
//                            // 上传成功，显示成功消息
//                            self.showAlert(
//                                title: "上传成功", 
//                                message: "作品ID: \(worksId)"
//                            )
//                        } else {
//                            // 获取错误信息
//                            let errorMsg = response.errMsg.isEmpty ? "服务器错误" : response.errMsg
//                            
//                            // 上传失败，显示错误消息
//                            self.showAlert(
//                                title: "上传失败", 
//                                message: errorMsg
//                            )
//                        }
//                        
//                    case .failure(let error):
//                        // 处理错误
//                        self.showAlert(
//                            title: "上传失败", 
//                            message: error.errorMessage
//                        )
//                    }
//                }
//            }
//        }
//    }
}

// MARK: - 裁剪视图代理
protocol VideoTrimViewDelegate: AnyObject {
    func didChangeTrimPosition(startTime: CGFloat, endTime: CGFloat)
    func didChangePreviewPosition(time: CGFloat)
}

extension VideoEditTestViewController: VideoTrimViewDelegate {
    // 在 didChangeTrimPosition 和 didChangePreviewPosition 中重置播放状态
    func didChangeTrimPosition(startTime: CGFloat, endTime: CGFloat) {
        self.startTime = startTime
        self.endTime = endTime
        
        // 更新时间范围标签
        updateTimeRangeLabel()
        
        // 如果当前播放位置不在新的裁剪区域内，则重置到开始位置
        if lastPlaybackTime < startTime || lastPlaybackTime > endTime {
            lastPlaybackTime = startTime
        }
        
        // 预览起始帧
        videoEditor?.preview(atTime: lastPlaybackTime)
        
        // 如果正在播放，从当前位置开始播放新的时间范围
        if isPlaying {
            videoEditor?.startPlay(fromTime: lastPlaybackTime, toTime: endTime)
        }
    }
    
    func didChangePreviewPosition(time: CGFloat) {
        // 更新视频预览位置
        videoEditor?.preview(atTime: time)
    }
}

// MARK: - 视频裁剪视图
class VideoTrimView: UIView {
    
    weak var delegate: VideoTrimViewDelegate?
    
    private var leftHandleView: UIView!
    private var rightHandleView: UIView!
    private var trimAreaView: UIView!
    private var videoDuration: CGFloat = 0
    
    private var leftHandlePosition: CGFloat = 0
    private var rightHandlePosition: CGFloat = 0
    
    // 添加这两个变量来存储当前的时间范围
    private var currentStartTime: CGFloat = 0
    private var currentEndTime: CGFloat = 0
    
    private var previewIndicator: UIView!  // 预览指示器（竖线）
    private var isDraggingPreviewIndicator = false
    private var previewIndicatorPosition: CGFloat = 0
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    private func setupView() {
        // 创建半透明遮罩
        let maskView = UIView()
        maskView.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        addSubview(maskView)
        
        // 创建裁剪区域视图
        trimAreaView = UIView()
        trimAreaView.backgroundColor = .clear
        trimAreaView.layer.borderColor = UIColor.white.cgColor
        trimAreaView.layer.borderWidth = 2
        addSubview(trimAreaView)
        
        // 左侧手柄
        leftHandleView = createHandleView()
        addSubview(leftHandleView)
        
        // 右侧手柄
        rightHandleView = createHandleView()
        addSubview(rightHandleView)
        
        // 创建预览指示器（竖线）- 使用更明显的颜色和宽度
        previewIndicator = UIView()
        previewIndicator.backgroundColor = .red // 使用红色使其更醒目
        previewIndicator.layer.cornerRadius = 2
        previewIndicator.frame = CGRect(x: 0, y: 0, width: 4, height: bounds.height)
        addSubview(previewIndicator)
        
        // 添加预览指示器的拖动手势
        let previewPanGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePreviewDrag(_:)))
        previewIndicator.addGestureRecognizer(previewPanGesture)
        previewIndicator.isUserInteractionEnabled = true
        
        // 设置手势识别器
        let leftPanGesture = UIPanGestureRecognizer(target: self, action: #selector(handleLeftDrag(_:)))
        leftHandleView.addGestureRecognizer(leftPanGesture)
        
        let rightPanGesture = UIPanGestureRecognizer(target: self, action: #selector(handleRightDrag(_:)))
        rightHandleView.addGestureRecognizer(rightPanGesture)
        
        // 设置约束
        maskView.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            maskView.topAnchor.constraint(equalTo: topAnchor),
            maskView.bottomAnchor.constraint(equalTo: bottomAnchor),
            maskView.leadingAnchor.constraint(equalTo: leadingAnchor),
            maskView.trailingAnchor.constraint(equalTo: trailingAnchor)
        ])
        
        // 不使用约束，我们将手动控制预览指示器的位置
    }
    
    private func createHandleView() -> UIView {
        let handle = UIView()
        handle.backgroundColor = .white
        handle.layer.cornerRadius = 3
        handle.isUserInteractionEnabled = true
        handle.frame = CGRect(x: 0, y: 0, width: 20, height: bounds.height)
        
        // 添加内部指示器
        let indicator = UIView(frame: CGRect(x: 9, y: 10, width: 2, height: bounds.height - 20))
        indicator.backgroundColor = .systemBlue
        handle.addSubview(indicator)
        
        return handle
    }
    
    func setupTrimmer(videoDuration: CGFloat) {
        self.videoDuration = videoDuration
        
        // 初始化位置
        leftHandlePosition = 0
        rightHandlePosition = bounds.width
        previewIndicatorPosition = leftHandlePosition  // 初始化预览指示器位置
        
        updateHandlePositions()
        updatePreviewIndicator()  // 显式调用更新预览指示器
        notifyDelegate()
    }
    
    private func updateHandlePositions() {
        let handleWidth: CGFloat = 20
        
        // 更新裁剪区域和手柄的位置
        trimAreaView.frame = CGRect(
            x: leftHandlePosition,
            y: 0,
            width: rightHandlePosition - leftHandlePosition,
            height: bounds.height
        )
        
        leftHandleView.frame = CGRect(
            x: leftHandlePosition - handleWidth/2,
            y: 0,
            width: handleWidth,
            height: bounds.height
        )
        
        rightHandleView.frame = CGRect(
            x: rightHandlePosition - handleWidth/2,
            y: 0,
            width: handleWidth,
            height: bounds.height
        )
    }
    
    private func notifyDelegate() {
        // 计算对应的视频时间
        let startTime = (leftHandlePosition / bounds.width) * videoDuration
        let endTime = (rightHandlePosition / bounds.width) * videoDuration
        
        // 存储当前的时间范围用于预览指示器计算
        self.currentStartTime = startTime
        self.currentEndTime = endTime
        
        // 通知代理完成裁剪位置变更
        delegate?.didChangeTrimPosition(startTime: startTime, endTime: endTime)
    }
    
    @objc private func handleLeftDrag(_ gesture: UIPanGestureRecognizer) {
        let translation = gesture.translation(in: self)
        gesture.setTranslation(.zero, in: self)
        
        leftHandlePosition = max(0, min(rightHandlePosition - 30, leftHandlePosition + translation.x))
        updateHandlePositions()
        
        // 实时更新预览指示器和视频预览
        updatePreviewIndicator()
        
        // 计算当前时间并实时通知代理
        let startTime = (leftHandlePosition / bounds.width) * videoDuration
        self.currentStartTime = startTime
        
        // 如果在拖动左侧滑块，同时更新预览位置到左侧滑块位置
        previewIndicatorPosition = leftHandlePosition + 10  // 滑块中心点
        updatePreviewIndicator()
        
        // 通知代理更新预览帧
        delegate?.didChangePreviewPosition(time: startTime)
        
        // 如果是结束拖动，更新裁剪区域
        if gesture.state == .ended || gesture.state == .cancelled {
            notifyDelegate()
        }
    }
    
    @objc private func handleRightDrag(_ gesture: UIPanGestureRecognizer) {
        let translation = gesture.translation(in: self)
        gesture.setTranslation(.zero, in: self)
        
        rightHandlePosition = min(bounds.width, max(leftHandlePosition + 30, rightHandlePosition + translation.x))
        updateHandlePositions()
        
        // 实时更新预览指示器和视频预览
        updatePreviewIndicator()
        
        // 计算当前时间并实时通知代理
        let endTime = (rightHandlePosition / bounds.width) * videoDuration
        self.currentEndTime = endTime
        
        // 如果在拖动右侧滑块，同时更新预览位置到右侧滑块位置
        previewIndicatorPosition = rightHandlePosition - 10  // 滑块中心点
        updatePreviewIndicator()
        
        // 通知代理更新预览帧
        delegate?.didChangePreviewPosition(time: endTime)
        
        // 如果是结束拖动，更新裁剪区域
        if gesture.state == .ended || gesture.state == .cancelled {
            notifyDelegate()
        }
    }
    
    // 修改 handlePreviewDrag 方法以提高手势响应
    @objc private func handlePreviewDrag(_ gesture: UIPanGestureRecognizer) {
        let translation = gesture.translation(in: self)
        gesture.setTranslation(.zero, in: self)
        
        switch gesture.state {
        case .began:
            isDraggingPreviewIndicator = true
        case .changed:
            // 使用更灵敏的拖动响应
            let dragRate: CGFloat = 1.0 // 可以调整为更大的值使移动更灵敏
            let handleWidth: CGFloat = 20
            let minX = leftHandlePosition + handleWidth/2
            let maxX = rightHandlePosition - handleWidth/2
            
            previewIndicatorPosition = max(minX, min(maxX, previewIndicatorPosition + translation.x * dragRate))
            updatePreviewIndicator()
            
            // 使用存储的时间范围变量计算预览时间
            let progress = (previewIndicatorPosition - leftHandlePosition) / (rightHandlePosition - leftHandlePosition)
            let previewTime = currentStartTime + (currentEndTime - currentStartTime) * progress
            delegate?.didChangePreviewPosition(time: previewTime)
            
        case .ended, .cancelled:
            isDraggingPreviewIndicator = false
        default:
            break
        }
    }
    
    // 修改 updatePreviewIndicator 方法
    private func updatePreviewIndicator() {
        // 确保预览指示器在左右手柄之间
        let handleWidth: CGFloat = 20
        let minX = leftHandlePosition + handleWidth/2
        let maxX = rightHandlePosition - handleWidth/2
        
        // 更新预览指示器位置和大小
        previewIndicator.frame = CGRect(
            x: max(minX, min(maxX, previewIndicatorPosition)) - 2, // 中心点偏移2像素（宽度的一半）
            y: 0,
            width: 4, // 固定宽度为4
            height: bounds.height // 高度等于视图高度
        )
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        updateHandlePositions()
        updatePreviewIndicator() // 确保每次布局更新都会更新预览指示器
    }

    // 增加触摸区域大小的常量
    private let touchAreaIncrease: CGFloat = 20
    
    // 增加方法用于更新播放进度
    func updatePreviewIndicatorForPlayback(progress: CGFloat) {
        guard progress >= 0, progress <= 1 else { return }
        
        // 根据进度计算预览指示器的位置
        let position = leftHandlePosition + (rightHandlePosition - leftHandlePosition) * progress
        previewIndicatorPosition = position
        updatePreviewIndicator()
    }
    
    
    
    // 优化触摸区域 - 重写 point(inside:with:) 方法
    override func point(inside point: CGPoint, with event: UIEvent?) -> Bool {
        // 检查是否在预览指示器的扩大区域内
        if previewIndicator.frame.insetBy(dx: -touchAreaIncrease, dy: 0).contains(point) {
            return true
        }
        
        // 检查是否在左侧手柄的扩大区域内
        if leftHandleView.frame.insetBy(dx: -touchAreaIncrease, dy: 0).contains(point) {
            return true
        }
        
        // 检查是否在右侧手柄的扩大区域内
        if rightHandleView.frame.insetBy(dx: -touchAreaIncrease, dy: 0).contains(point) {
            return true
        }
        
        // 否则使用默认的点击检测
        return super.point(inside: point, with: event)
    }
    
    
}

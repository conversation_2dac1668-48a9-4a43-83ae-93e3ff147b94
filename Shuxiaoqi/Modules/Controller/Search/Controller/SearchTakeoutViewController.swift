//搜索-外卖
import UIKit
import SnapKit
import Kingfisher
import CoreLocation

class SearchTakeoutViewController: UIViewController, Searchable {
    // MARK: - UI 组件
    private let filterContainer = UIView()
    private var filterButtons: [UIButton] = []
    private var selectedFilterIndex: Int = 0

    private lazy var tableView: UITableView = {
        let tv = UITableView(frame: .zero, style: .plain)
        tv.backgroundColor = UIColor(hex: "#F5F5F5")
        tv.separatorStyle = .none
        tv.dataSource = self
        tv.delegate = self
        tv.register(TakeoutTableViewCell.self, forCellReuseIdentifier: TakeoutTableViewCell.reuseIdentifier)
        return tv
    }()

    // MARK: - 数据模型
    // 删除本地 `GroupBuyShopItem`，使用 `SearchShopGoodModels.swift` 中的公共定义，避免重复声明导致的编译冲突。
    private var takeouts: [TakeoutShopItem] = []

    /// 空数据占位
    private lazy var emptyStateView: UIView = {
        let v = UIView()
        let img = UIImageView(image: UIImage(named: "empty_data_placeholder_image"))
        img.contentMode = .scaleAspectFit
        img.translatesAutoresizingMaskIntoConstraints = false
        v.addSubview(img)
        let lbl = UILabel()
        lbl.text = "暂无相关外卖店铺"
        lbl.textColor = .lightGray
        lbl.font = .systemFont(ofSize: 14)
        lbl.translatesAutoresizingMaskIntoConstraints = false
        v.addSubview(lbl)
        NSLayoutConstraint.activate([
            img.centerXAnchor.constraint(equalTo: v.centerXAnchor),
            img.centerYAnchor.constraint(equalTo: v.centerYAnchor, constant: -20),
            img.widthAnchor.constraint(equalToConstant: 120),
            img.heightAnchor.constraint(equalToConstant: 120),
            lbl.topAnchor.constraint(equalTo: img.bottomAnchor, constant: 12),
            lbl.centerXAnchor.constraint(equalTo: v.centerXAnchor)
        ])
        return v
    }()

    private let locationManager = CLLocationManager()
    private var currentLatitude: Double?
    private var currentLongitude: Double?
    private var currentKeyword: String?
    /// 缓存不同关键词对应的数据，key 为关键词(nil 用 "" 表示)
    private var keywordCache: [String: [TakeoutShopItem]] = [:]
    /// 当定位尚未就绪时暂存待搜索的关键词
    private var pendingKeyword: String?

    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .white
        setupLocation()
        setupUI()
        // 初始不主动请求数据，等待用户输入或切换触发
        updateEmptyState()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        // 当控制器再次显示时，确保选中按钮的渐变背景正常显示
        updateFilterSelectionUI()
    }

    // MARK: - UI 布局
    private func setupUI() {
        view.addSubview(filterContainer)
        view.addSubview(tableView)

        setupFilterBar()

        filterContainer.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }

        tableView.snp.makeConstraints { make in
            make.top.equalTo(filterContainer.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
    }

    private func setupFilterBar() {
        let titles = ["综合排序", "最新上线"]
        var previousButton: UIButton?
        for (index, title) in titles.enumerated() {
            let button = UIButton(type: .custom)
            button.setTitle(title, for: .normal)
            button.setTitleColor(UIColor(hex: "#777777"), for: .normal)
            button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
            button.tag = index
            button.addTarget(self, action: #selector(filterButtonTapped(_:)), for: .touchUpInside)
            filterContainer.addSubview(button)

            button.snp.makeConstraints { make in
                make.centerY.equalToSuperview()
                if let prev = previousButton {
                    make.left.equalTo(prev.snp.right).offset(24)
                } else {
                    make.left.equalToSuperview().offset(16)
                }
                make.width.equalTo(84)
                make.height.equalTo(29)
            }

            filterButtons.append(button)
            previousButton = button
        }

        if let first = filterButtons.first {
            selectedFilterIndex = first.tag
        }
    }

    @objc private func filterButtonTapped(_ sender: UIButton) {
        guard sender.tag != selectedFilterIndex else { return }
        selectedFilterIndex = sender.tag
        updateFilterSelectionUI()
        // TODO: 根据筛选重新排序数据
    }

    private func updateFilterSelectionUI() {
        for btn in filterButtons {
            let isSelected = btn.tag == selectedFilterIndex
            if isSelected {
                btn.setTitleColor(.white, for: .normal)
                btn.titleLabel?.font = UIFont.boldSystemFont(ofSize: 14)
                btn.backgroundColor = .clear
                btn.layer.cornerRadius = 14.5
                applyGradient(to: btn)
                filterContainer.bringSubviewToFront(btn)
            } else {
                btn.setTitleColor(UIColor(hex: "#777777"), for: .normal)
                btn.titleLabel?.font = UIFont.systemFont(ofSize: 14)
                removeGradient(from: btn)
                btn.backgroundColor = UIColor(hex: "#E7E7E7")
                btn.layer.cornerRadius = 14.5
            }
        }
    }

    // MARK: - 渐变
    private func applyGradient(to button: UIButton) {
        removeGradient(from: button)
        let gradient = CAGradientLayer()
        gradient.colors = [
            UIColor(hex: "#FF8D36").cgColor,
            UIColor(hex: "#FF5858").cgColor
        ]
        gradient.startPoint = CGPoint(x: 0, y: 0.5)
        gradient.endPoint = CGPoint(x: 1, y: 0.5)
        gradient.cornerRadius = 14.5
        gradient.frame = button.bounds
        gradient.name = "btnGradient"
        button.layer.insertSublayer(gradient, at: 0)
    }

    private func removeGradient(from button: UIButton) {
        button.layer.sublayers?.removeAll(where: { $0.name == "btnGradient" })
    }

    private var didApplyInitial = false

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        if !didApplyInitial {
            updateFilterSelectionUI()
            didApplyInitial = true
        }
        for btn in filterButtons {
            btn.layer.sublayers?.forEach { layer in
                if layer.name == "btnGradient" {
                    layer.frame = btn.bounds
                }
            }
        }
    }

    // MARK: - 定位
    private func setupLocation() {
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyHundredMeters
        if CLLocationManager.authorizationStatus() == .notDetermined {
            locationManager.requestWhenInUseAuthorization()
        } else {
            locationManager.startUpdatingLocation()
        }
        print("[Location] setupLocation called, auth status: \(CLLocationManager.authorizationStatus().rawValue)")
    }

    // MARK: - 网络请求
    private func fetchTakeoutShops(keyword: String?) {
        let lat = currentLatitude ?? 0
        let lon = currentLongitude ?? 0
        let request = TakeoutShopSearchRequest(latitude: lat, longitude: lon, areaId: nil, page: 0, size: 10, smartBy: nil, categoryId: nil, priceNum: nil, distance: nil, searchValue: keyword)
        APIManager.shared.searchTakeoutShop(request: request) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    self?.takeouts = response.data.list
                    // 缓存结果
                    let key = keyword ?? ""
                    self?.keywordCache[key] = self?.takeouts
                    self?.tableView.reloadData()
                case .failure(let error):
                    print("Takeout search error: \(error)")
                }
                self?.updateEmptyState()
            }
        }
    }

    // MARK: - Searchable
    func search(with keyword: String) {
        print("[Takeout] 搜索关键词：\(keyword)")

        // 已经加载过相同关键词且有数据 -> 直接使用缓存
        let key = keyword
        if let cached = keywordCache[key], !cached.isEmpty {
            print("[Takeout] 使用缓存，跳过请求")
            currentKeyword = keyword
            takeouts = cached
            tableView.reloadData()
            updateEmptyState()
            return
        }

        // 若经纬度尚未获取，先请求定位并记录待搜索关键词
        guard let lat = currentLatitude, let lon = currentLongitude, lat != 0, lon != 0 else {
            print("[Takeout] 定位未就绪，等待定位后再请求")
            pendingKeyword = keyword
            ensureLocationAuthorization()
            return
        }

        currentKeyword = keyword
        fetchTakeoutShops(keyword: keyword)
    }

    private func updateEmptyState() {
        tableView.backgroundView = takeouts.isEmpty ? emptyStateView : nil
    }

    /// 对外暴露：确保已请求定位权限，若未授权则立即请求
    func ensureLocationAuthorization() {
        switch CLLocationManager.authorizationStatus() {
        case .notDetermined:
            locationManager.requestWhenInUseAuthorization()
        case .authorizedAlways, .authorizedWhenInUse:
            locationManager.startUpdatingLocation()
        default:
            presentLocationDeniedAlert()
        }
    }

    /// 弹窗提醒用户开启定位
    private func presentLocationDeniedAlert() {
        let alert = UIAlertController(title: "需要位置信息",
                                      message: "为了获取附近外卖店铺，请在\"设置-隐私-定位服务\"中允许树小柒访问您的位置。",
                                      preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(UIAlertAction(title: "前往设置", style: .default) { _ in
            guard let url = URL(string: UIApplication.openSettingsURLString),
                  UIApplication.shared.canOpenURL(url) else { return }
            UIApplication.shared.open(url)
        })
        present(alert, animated: true)
    }
}

// MARK: - UITableViewDataSource & Delegate
extension SearchTakeoutViewController: UITableViewDataSource, UITableViewDelegate {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int { takeouts.count }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: TakeoutTableViewCell.reuseIdentifier, for: indexPath) as? TakeoutTableViewCell else { return UITableViewCell() }
        cell.configure(with: takeouts[indexPath.row])
        return cell
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 180
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        // TODO: 进入商品详情或商家详情
        let item = takeouts[indexPath.row].goodResultVO
        let webVC = WebViewController(path: "localLifePage/storePage/storePage?shopId=\(String(describing: item?.goodId))&pageType=takeOut", title: "外卖详情")
        navigationController?.pushViewController(webVC, animated: true)
    }
}

// MARK: - CLLocationManagerDelegate
extension SearchTakeoutViewController: CLLocationManagerDelegate {
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        print("[Location] didUpdateLocations callback with count: \(locations.count)")
        guard let loc = locations.last else { return }
        print("[Location] new coordinate: \(loc.coordinate.latitude), \(loc.coordinate.longitude)")
        currentLatitude = loc.coordinate.latitude
        currentLongitude = loc.coordinate.longitude
        manager.stopUpdatingLocation()

        // 若有待处理的关键词，定位就绪后立刻发起请求
        if let keywordToSearch = pendingKeyword {
            print("[Location] 定位完成，继续搜索关键词：\(keywordToSearch)")
            pendingKeyword = nil
            fetchTakeoutShops(keyword: keywordToSearch)
        }
    }

    func locationManagerDidChangeAuthorization(_ manager: CLLocationManager) {
        switch manager.authorizationStatus {
        case .authorizedAlways, .authorizedWhenInUse:
            print("[Location] authorization granted, startUpdatingLocation")
            manager.startUpdatingLocation()
        case .denied, .restricted:
            print("[Location] authorization denied or restricted")
            presentLocationDeniedAlert()
        case .notDetermined:
            break
        @unknown default:
            break
        }
    }
}

//{
//    "status": 200,
//    "errMsg": "",
//    "data": {
//        "pageSize": 10,
//        "pageNum": 0,
//        "total": 2,
//        "list": [
//            {
//                "activeName": "",
//                "distance": 0,
//                "shopHeadImage": "https://image.gzyoushu.com/e12e74f107a44f50bedc2d8f45f7ed37.jpg",
//                "shopImage": "",
//                "latitude": "23.140919",
//                "shopName": "测试小店(勿下单)",
//                "saleNum": 30,
//                "shopAddress": "保利中宇广场A座",
//                "categoryName": "加油充电",
//                "minute": 0,
//                "shopBusinessStatus": "20",
//                "orderPriceAvg": "0.47",
//                "deliveryFee": 0,
//                "shopScore": 0,
//                "startFee": 10,
//                "shopId": "ff808081939feff40193a07f88970002",
//                "labelName": "",
//                "goodResultVO": {
//                    "activePrice": 0.02,
//                    "shopSecCategoryId": 373,
//                    "shopCategoryId": 369,
//                    "minSaleNum": 1,
//                    "skuResultVOList": [
//                        {
//                            "skuName": "模块",
//                            "weightNuM": 1,
//                            "id": 428,
//                            "goodId": 491,
//                            "skuItemResultVOList": [
//                                {
//                                    "activePrice": null,
//                                    "stockType": 1,
//                                    "isSelect": 2,
//                                    "goodId": 491,
//                                    "saleStatus": 1,
//                                    "autoNum": true,
//                                    "skuName": "魄力",
//                                    "createTime": "2025-01-23 17:54:50",
//                                    "price": 1,
//                                    "totalStock": 1,
//                                    "id": 467,
//                                    "stringPrice": "0.01",
//                                    "saleStock": 0,
//                                    "supplyNum": 0,
//                                    "skuId": 428,
//                                    "deleteState": 0
//                                },
//                                {
//                                    "activePrice": null,
//                                    "stockType": 1,
//                                    "isSelect": 2,
//                                    "goodId": 491,
//                                    "saleStatus": 1,
//                                    "autoNum": true,
//                                    "skuName": "连连看",
//                                    "createTime": "2025-01-24 13:43:15",
//                                    "price": 1,
//                                    "totalStock": 1,
//                                    "id": 468,
//                                    "stringPrice": "0.01",
//                                    "saleStock": 0,
//                                    "supplyNum": 0,
//                                    "skuId": 428,
//                                    "deleteState": 0
//                                }
//                            ]
//                        },
//                        {
//                            "skuName": "预期",
//                            "weightNuM": 1,
//                            "id": 429,
//                            "goodId": 491,
//                            "skuItemResultVOList": [
//                                {
//                                    "activePrice": null,
//                                    "stockType": 1,
//                                    "isSelect": 2,
//                                    "goodId": 491,
//                                    "saleStatus": 1,
//                                    "autoNum": true,
//                                    "skuName": "宣传费",
//                                    "createTime": "2025-01-24 13:44:18",
//                                    "price": 1,
//                                    "totalStock": 1,
//                                    "id": 469,
//                                    "stringPrice": "0.01",
//                                    "saleStock": 0,
//                                    "supplyNum": 0,
//                                    "skuId": 429,
//                                    "deleteState": 0
//                                },
//                                {
//                                    "activePrice": null,
//                                    "stockType": 1,
//                                    "isSelect": 2,
//                                    "goodId": 491,
//                                    "saleStatus": 1,
//                                    "autoNum": true,
//                                    "skuName": "户籍",
//                                    "createTime": "2025-01-24 13:44:18",
//                                    "price": 1,
//                                    "totalStock": 1,
//                                    "id": 470,
//                                    "stringPrice": "0.01",
//                                    "saleStock": 0,
//                                    "supplyNum": 0,
//                                    "skuId": 429,
//                                    "deleteState": 0
//                                }
//                            ]
//                        }
//                    ],
//                    "goodId": 491,
//                    "shopFirstCategoryId": 369,
//                    "sourcePrice": "0.02",
//                    "goodName": "测试双边库存1",
//                    "totalStock": 4,
//                    "labelNameList": [
//                        "商家推荐"
//                    ],
//                    "goodImages": "https://image.gzyoushu.com/a578dc4182834e7f93621d0d4a4e821b.jpg",
//                    "saleStock": 2
//                },
//                "longitude": "113.341835"
//            }
//        ],
//        "mentionedUser": null,
//        "empty": false
//    },
//    "msg": "成功"
//}

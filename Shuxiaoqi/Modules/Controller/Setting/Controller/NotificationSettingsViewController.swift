//
//  NotificationSettingsViewController.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yongsheng ye on 2025/3/20.
//

//  通知设置

import UIKit
import SnapKit

class NotificationSettingsViewController: BaseViewController, UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    
    // MARK: - 模型
    
    // 通知设置项模型
    struct NotificationSetting {
        let id: Int           // 设置项ID
        let title: String       // 设置项标题
        let description: String // 设置项描述（可选）
        var isEnabled: Bool     // 是否启用
        let category: String    // 分类：商城、互动等
        let type: SettingType   // 新增：类型（switch/selector）
        var selectorValue: String? // 选择器当前值（如有）
    }
    
    enum SettingType {
        case `switch`   // 普通开关
        case selector   // 选择器
    }
    
    // 分类标题模型
    struct CategorySection {
        let title: String       // 分类标题
        let settings: [NotificationSetting] // 该分类下的设置项
    }
    
    // 通知设置数据
    private var categories: [CategorySection] = []
    
    // MARK: - UI组件
    
    // 顶部提示语Label
    private let topTipLabel: UILabel = {
        let label = UILabel()
        label.text = "快捷设置·接受谁的通知"
        label.font = .systemFont(ofSize: 17, weight: .medium)
        label.textColor = UIColor(hex: "#333333")
        label.textAlignment = .left
        return label
    }()
    
    // 滚动视图
    private let scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        scrollView.backgroundColor = UIColor(hex: "#F5F5F5")
        return scrollView
    }()
    
    // 内容视图
    private let notificationContentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    // 顶部按钮区 UICollectionView
    private var buttonCollectionView: UICollectionView!
    private var buttonConfig: [NoticeButtonConfigItem] = []
    private var selectedButtonIndex: Int = -1
    private var buttonCollectionViewHeightConstraint: Constraint? // 新增高度约束引用
    private var notificationContentViewTopConstraint: Constraint? // 新增内容区top约束引用
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        fetchNotificationSettings() // 用真实API
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        // 计算内容高度（多行）
        let layout = buttonCollectionView.collectionViewLayout
        let contentHeight = layout.collectionViewContentSize.height
        if abs((buttonCollectionView.frame.height - contentHeight)) > 1 {
            buttonCollectionViewHeightConstraint?.update(offset: contentHeight)
        }
    }
    
    // MARK: - 数据获取
    private func fetchNotificationSettings() {
        APIManager.shared.getUserNoticeSetting { [weak self] result in
            guard let self = self else { return }
            switch result {
            case .success(let responseData):
                if responseData.isSuccess, let data = responseData.data {
                    DispatchQueue.main.async {
                        self.buttonConfig = data.noticeButtonConfig
                        // 初始化选中index
                        if let selectedIdx = self.buttonConfig.firstIndex(where: { $0.isSelect }) {
                            self.selectedButtonIndex = selectedIdx
                        } else {
                            self.selectedButtonIndex = -1 // 全部未选中
                        }
                        self.buttonCollectionView.reloadData()
                        self.handleNotificationSettingsResponse(data.noticeSetConfig)
                    }
                } else {
                    print("获取通知设置失败 (业务逻辑): \(responseData.displayMessage)")
                }
            case .failure(let error):
                print("获取通知设置失败 (网络/解析): \(error.errorMessage)")
            }
        }
    }
    
    private func handleNotificationSettingsResponse(_ groups: [UserNoticeSettingGroup]) {
        // 将API响应数据转换为视图控制器使用的模型
        var newCategories: [CategorySection] = []
        for group in groups {
            var settings: [NotificationSetting] = []
            for item in group.item {
                let type: SettingType = item.isOption ? .selector : .switch
                let setting = NotificationSetting(
                    id: item.id,
                    title: item.name,
                    description: item.describe,
                    isEnabled: item.isEnabled,
                    category: group.groupName,
                    type: type,
                    selectorValue: item.selectorValue
                )
                settings.append(setting)
            }
            let category = CategorySection(title: group.groupName, settings: settings)
            newCategories.append(category)
        }
        self.categories = newCategories
        refreshSettingsUI()
    }
    
    // 辅助：state转selectorValue
    private func selectorValueFromState(_ state: Int) -> String {
        switch state {
        case 0: return "全部"
        case 1: return "我关注的人"
        case 2: return "互相关注的人"
        case 3: return "不接收"
        default: return "全部"
        }
    }
    // 辅助：selectorValue转state
    private func stateFromSelectorValue(_ value: String) -> Int {
        switch value {
        case "全部": return 0
        case "我关注的人": return 1
        case "互相关注的人": return 2
        case "不接收": return 3
        default: return 0
        }
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        navTitle = "通知设置"
        navBar.backgroundColor = UIColor(hex: "#F5F5F5")
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        // 添加顶部提示语
        contentView.addSubview(topTipLabel)
        topTipLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(13)
            make.left.equalTo(12)
            make.right.equalTo(-16)
        }
        // 顶部按钮区 UICollectionView 多行自适应
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 12
        layout.minimumInteritemSpacing = 12
        layout.sectionInset = UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 16)
        // 不用estimatedItemSize，完全靠sizeForItemAt
        buttonCollectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        buttonCollectionView.backgroundColor = UIColor(hex: "#F5F5F5")
        buttonCollectionView.showsHorizontalScrollIndicator = false
        buttonCollectionView.delegate = self
        buttonCollectionView.dataSource = self
        buttonCollectionView.register(NoticeButtonCell.self, forCellWithReuseIdentifier: "NoticeButtonCell")
        contentView.addSubview(buttonCollectionView)
        buttonCollectionView.snp.makeConstraints { make in
            make.top.equalTo(topTipLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview()
            self.buttonCollectionViewHeightConstraint = make.height.equalTo(45).constraint // 初始高度
        }
        // 添加滚动视图
        contentView.addSubview(scrollView)
        scrollView.snp.makeConstraints { make in
            self.notificationContentViewTopConstraint = make.top.equalTo(buttonCollectionView.snp.bottom).offset(0).constraint // 跟随collectionView
            make.left.right.bottom.equalToSuperview()
        }
        // 添加内容视图
        scrollView.addSubview(notificationContentView)
        notificationContentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(scrollView)
        }
    }
    
    // MARK: - UICollectionViewDataSource & Delegate
    
    // 按钮数量
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return buttonConfig.count
    }
    // 按钮Cell
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "NoticeButtonCell", for: indexPath) as? NoticeButtonCell else {
            return UICollectionViewCell()
        }
        let config = buttonConfig[indexPath.item]
        let isSelected = (selectedButtonIndex == indexPath.item)
        cell.configure(with: config, selected: isSelected)
        return cell
    }
    // 按钮点击
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        selectedButtonIndex = indexPath.item
        buttonCollectionView.reloadData()
        // 顶部按钮点击对接接口
        let config = buttonConfig[indexPath.item]
        // 只传递 noticeButtonId, configId 为空，并根据当前选择状态传递正确的 state (1-开启, 0-关闭)
        // 这里点击即视为开启该通知按钮，因此传递 state = 1
        APIManager.shared.setUserNoticeSet(noticeButtonId: config.id, noticeConfigId: nil, state: 1) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.isSuccess {
                        self.fetchNotificationSettings() // 刷新全部数据
                    } else {
                        self.showToast("更新失败: \(response.displayMessage)")
                    }
                case .failure(let error):
                    self.showToast("网络错误，更新失败")
                }
            }
        }
    }
    
    // MARK: - UICollectionViewDelegateFlowLayout
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let config = buttonConfig[indexPath.item]
        let text = config.buttonName
        let font = UIFont.systemFont(ofSize: 14, weight: .medium)
        let textWidth = (text as NSString).size(withAttributes: [.font: font]).width
        return CGSize(width: textWidth + 24, height: 35) // 左右各6pt
    }
    
    // MARK: - NoticeButtonCell
    class NoticeButtonCell: UICollectionViewCell {
        private let titleLabel = UILabel()
        override init(frame: CGRect) {
            super.init(frame: frame)
            contentView.backgroundColor = UIColor(hex: "#E7E7E7")
            contentView.layer.cornerRadius = 18
            contentView.layer.masksToBounds = true
            titleLabel.font = .systemFont(ofSize: 14, weight: .medium)
            titleLabel.textAlignment = .center
            titleLabel.numberOfLines = 1
            contentView.addSubview(titleLabel)
            titleLabel.snp.makeConstraints { make in
                make.top.bottom.equalToSuperview()
                make.left.equalToSuperview().offset(6)
                make.right.equalToSuperview().offset(-6)
            }
        }
        required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }
        func configure(with config: NoticeButtonConfigItem, selected: Bool) {
            titleLabel.text = config.buttonName
            contentView.backgroundColor = selected ? UIColor(hex: "#FF8F1F") : UIColor(hex: "#E7E7E7")
            titleLabel.textColor = selected ? .white : UIColor(hex: "#666666")
        }
    }
    
    // 创建分类标签
    private func createCategoryLabel(title: String) -> UILabel {
        let label = UILabel()
        label.text = title
        label.textColor = UIColor(hex: "#333333")
        label.font = .systemFont(ofSize: 16, weight: .bold)
        return label
    }
    
    // 创建设置项视图 - 支持开关和选择器
    private func createSettingView(setting: NotificationSetting, index: Int) -> UIView {
        let settingView = UIView()
        settingView.backgroundColor = .white
        settingView.layer.cornerRadius = 8
        // 设置项标题
        let titleLabel = UILabel()
        titleLabel.text = setting.title
        titleLabel.textColor = UIColor(hex: "#333333")
        titleLabel.font = .systemFont(ofSize: 16, weight: .medium)
        // 设置项描述
        let descriptionLabel = UILabel()
        descriptionLabel.text = setting.description
        descriptionLabel.textColor = UIColor(hex: "#999999")
        descriptionLabel.font = .systemFont(ofSize: 12)
        // 右侧控件
        if setting.type == .switch {
            let switchControl = UISwitch()
            switchControl.isOn = setting.isEnabled
            switchControl.onTintColor = UIColor(hex: "#FF8F1F")
            switchControl.transform = CGAffineTransform(scaleX: 0.75, y: 0.75)
            var categoryIndex = 0
            for (i, category) in categories.enumerated() {
                if category.title == setting.category {
                    categoryIndex = i
                    break
                }
            }
            switchControl.tag = categoryIndex * 100 + index
            switchControl.addTarget(self, action: #selector(switchValueChanged(_:)), for: .valueChanged)
            settingView.addSubview(switchControl)
            switchControl.snp.makeConstraints { make in
                make.centerY.equalToSuperview()
                make.right.equalTo(-16)
                make.height.equalTo(25)
            }
        } else if setting.type == .selector {
            let selectorButton = UIButton(type: .system)
            selectorButton.setTitle(setting.selectorValue ?? "全部", for: .normal)
            selectorButton.setTitleColor(UIColor(hex: "#777777"), for: .normal)
            selectorButton.titleLabel?.font = .systemFont(ofSize: 13, weight: .regular)
            selectorButton.contentHorizontalAlignment = .right
            // 添加右侧箭头图标
            let arrowImage = UIImage(named: "setting_arrow")?.withRenderingMode(.alwaysOriginal)
            selectorButton.setImage(arrowImage, for: .normal)
            selectorButton.semanticContentAttribute = .forceRightToLeft
            selectorButton.imageEdgeInsets = UIEdgeInsets(top: 0, left: 2, bottom: 0, right: -2) // 图标与文字间距1-2pt
            selectorButton.titleEdgeInsets = UIEdgeInsets(top: 0, left: -2, bottom: 0, right: 2)
            var categoryIndex = 0
            for (i, category) in categories.enumerated() {
                if category.title == setting.category {
                    categoryIndex = i
                    break
                }
            }
            selectorButton.tag = categoryIndex * 100 + index
            selectorButton.addTarget(self, action: #selector(selectorButtonTapped(_:)), for: .touchUpInside)
            settingView.addSubview(selectorButton)
            selectorButton.snp.makeConstraints { make in
                make.centerY.equalToSuperview()
                make.right.equalTo(-16)
                make.height.equalTo(25)
                make.width.greaterThanOrEqualTo(60)
            }
        }
        // 添加子视图
        settingView.addSubview(titleLabel)
        settingView.addSubview(descriptionLabel)
        // 设置约束
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(16)
            make.left.equalTo(16)
        }
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.left.equalTo(titleLabel)
            make.bottom.equalTo(-16)
        }
        return settingView
    }
    
    // MARK: - 事件处理
    
    @objc private func switchValueChanged(_ sender: UISwitch) {
        let categoryIndex = sender.tag / 100
        let settingIndex = sender.tag % 100
        guard categoryIndex < categories.count else { return }
        let category = categories[categoryIndex]
        guard settingIndex < category.settings.count else { return }
        var updatedSettings = category.settings
        updatedSettings[settingIndex].isEnabled = sender.isOn
        categories[categoryIndex] = CategorySection(title: category.title, settings: updatedSettings)
        let changedSetting = updatedSettings[settingIndex]
        // 对接API：传configId和state
        let state = sender.isOn ? 1 : 0
        APIManager.shared.setUserNoticeSet(noticeButtonId: nil, noticeConfigId: changedSetting.id, state: state) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.isSuccess {
                        self.fetchNotificationSettings() // 操作成功后刷新整个列表，确保顶部按钮状态同步
                    } else {
                        self.showToast("更新失败: \(response.displayMessage)")
                    }
                case .failure(_):
                    self.showToast("网络错误，更新失败")
                }
            }
        }
    }
    
    // 选择器按钮点击事件
    @objc private func selectorButtonTapped(_ sender: UIButton) {
        let categoryIndex = sender.tag / 100
        let settingIndex = sender.tag % 100
        guard categoryIndex < categories.count else { return }
        let category = categories[categoryIndex]
        guard settingIndex < category.settings.count else { return }
        var setting = category.settings[settingIndex]
        let options = ["全部", "我关注的人", "互相关注的人", "不接收"]
        let selectedIdx = options.firstIndex(of: setting.selectorValue ?? "全部") ?? 0
        let sheet = NotificationPopSelector(options: options, selectedIndex: selectedIdx) { [weak self] idx in
            guard let self = self else { return }
            // 更新本地状态
            setting.selectorValue = options[idx]
            var updatedSettings = category.settings
            updatedSettings[settingIndex] = setting
            categories[categoryIndex] = CategorySection(title: category.title, settings: updatedSettings)
            self.refreshSettingsUI()
            // 对接API：传configId和state
            let state = self.stateFromSelectorValue(options[idx])
            APIManager.shared.setUserNoticeSet(noticeButtonId: nil, noticeConfigId: setting.id, state: state) { [weak self] result in
                guard let self = self else { return }
                DispatchQueue.main.async {
                    switch result {
                    case .success(let response):
                        if response.isSuccess {
                            self.fetchNotificationSettings() // 操作成功后刷新整个列表，确保顶部按钮状态同步
                        } else {
                            self.showToast("更新失败: \(response.displayMessage)")
                        }
                    case .failure(_):
                        self.showToast("网络错误，更新失败")
                    }
                }
            }
        }
        if let window = UIApplication.shared.windows.first(where: { $0.isKeyWindow }) {
            sheet.show(in: window)
        }
    }
    
    // 刷新设置项UI
    private func refreshSettingsUI() {
        // 移除所有子视图并重新创建
        for subview in notificationContentView.subviews {
            subview.removeFromSuperview()
        }
        
        // 如果没有数据，直接返回
        if categories.isEmpty {
            return
        }
        
        // 重新创建UI
        var lastView: UIView = UIView() // 临时视图
        
        for (index, category) in categories.enumerated() {
            // 创建分类标题
            let categoryLabel = createCategoryLabel(title: category.title)
            notificationContentView.addSubview(categoryLabel)
            
            // 第一个分类标题需要有额外的顶部间距
            if index == 0 {
                categoryLabel.snp.makeConstraints { make in
                    make.top.equalToSuperview().offset(8) // 距顶部8pt
                    make.left.equalTo(16)
                    make.right.equalTo(-16)
                }
            } else {
                categoryLabel.snp.makeConstraints { make in
                    make.top.equalTo(lastView.snp.bottom).offset(16)
                    make.left.equalTo(16)
                    make.right.equalTo(-16)
                }
            }
            
            lastView = categoryLabel
            
            // 创建该分类下的设置项
            for (settingIndex, setting) in category.settings.enumerated() {
                let settingView = createSettingView(setting: setting, index: settingIndex)
                notificationContentView.addSubview(settingView)
                settingView.snp.makeConstraints { make in
                    make.top.equalTo(lastView.snp.bottom).offset(10)
                    make.left.equalTo(16)
                    make.right.equalTo(-16)
                }
                
                lastView = settingView
            }
        }
        
        // 设置内容视图底部约束
        lastView.snp.makeConstraints { make in
            make.bottom.equalTo(notificationContentView).offset(-16)
        }
    }
}

import UIKit
import Kingfisher
import SnapKit

// 滤镜数据模型
struct FilterItem {
    let id: Int
    let name: String
    let describeImg: String
    let contentFile: String
    var isDownloaded: Bool = false
    var isSelected: Bool = false
    var intensity: Float = 0.0 // 滤镜强度默认值改为0
}

// 滤镜面板代理
protocol FilterPanelViewDelegate: AnyObject {
    func filterPanelDidSelectFilter(_ filter: FilterItem)
    func filterPanelDidTapDownload(for filter: FilterItem)
    func filterPanelDidChangeIntensity(_ intensity: Float, for filter: FilterItem)
    func filterPanelDidTouchOriginal(_ state: UIGestureRecognizer.State)
}
    
class FilterPanelView: UIView {
    
    // MARK: - Properties
    
    weak var delegate: FilterPanelViewDelegate?
    
    private var filterItems: [FilterItem] = []
    private var selectedFilterId: Int?
    private var currentItemIndex: Int = 0

    // 保存原始滤镜强度的字典
    private var originalIntensities: [Int: Float] = [:]
    
    // MARK: - UI Components
    
    // 背景模糊效果
    private lazy var blurView: UIVisualEffectView = {
        let blurEffect = UIBlurEffect(style: .dark)
        let view = UIVisualEffectView(effect: blurEffect)
        return view
    }()
    
    // 顶部容器（包含滑块区域）
    private lazy var topContainerView: UIView = {
        let view = UIView()
        return view
    }()
    
    // 重置按钮
    private lazy var resetButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "icon_filter_reset_btn"), for: .normal)
        button.tintColor = .white
        button.addTarget(self, action: #selector(resetButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 强度滑块
    private lazy var intensitySlider: UISlider = {
        let slider = UISlider()
        slider.minimumValue = 0.0
        slider.maximumValue = 1.0
        slider.value = 0.0
        slider.minimumTrackTintColor = .white
        slider.maximumTrackTintColor = UIColor.white.withAlphaComponent(0.3)
        slider.setThumbImage(createThumbImage(size: CGSize(width: 12, height: 12)), for: .normal)
        slider.addTarget(self, action: #selector(sliderValueChanged(_:)), for: .valueChanged)
        slider.addTarget(self, action: #selector(sliderTouchEnded), for: [.touchUpInside, .touchUpOutside])
        return slider
    }()
    
    // 强度值标签
    private lazy var valueLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.white
        label.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        label.textAlignment = .center
        label.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        label.layer.cornerRadius = 8
        label.layer.masksToBounds = true
        label.isHidden = true
        return label
    }()
    
    // 参考标记视图
    private lazy var referenceMarkView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white.withAlphaComponent(0.7)
        view.layer.cornerRadius = 3
        view.isHidden = true
        return view
    }()
    
    // 原图标签（用于对比当前滤镜的0强度和用户设置强度）
    private lazy var originalLabel: UILabel = {
        let label = UILabel()
        label.text = "原图"
        label.textColor = UIColor.white.withAlphaComponent(0.7)
        label.font = UIFont.systemFont(ofSize: 13)
        label.textAlignment = .center
        label.isUserInteractionEnabled = true
        let longPress = UILongPressGestureRecognizer(target: self, action: #selector(originalLabelLongPressed(_:)))
        longPress.minimumPressDuration = 0.1
        label.addGestureRecognizer(longPress)
        return label
    }()
    
    // 底部滤镜容器
    private lazy var bottomContainerView: UIView = {
        let view = UIView()
        return view
    }()

    // 滤镜滚动视图
    private lazy var filterScrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.showsVerticalScrollIndicator = false
        return scrollView
    }()

    // 滤镜容器视图
    private lazy var filterContainerView: UIView = {
        let view = UIView()
        return view
    }()
    
    // 加载指示器
    private lazy var loadingIndicator: UIActivityIndicatorView = {
        let indicator = UIActivityIndicatorView(style: .medium)
        indicator.color = UIColor.white
        indicator.hidesWhenStopped = true
        return indicator
    }()
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        fetchFilterList()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        fetchFilterList()
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        backgroundColor = UIColor.black.withAlphaComponent(0.9) // 加深背景颜色

        // 移除模糊背景视图（移除透明蒙版效果）
        // addSubview(blurView)
        addSubview(topContainerView)
        topContainerView.addSubview(resetButton)
        topContainerView.addSubview(intensitySlider)
        topContainerView.addSubview(originalLabel)
        
        addSubview(valueLabel)
        addSubview(referenceMarkView)
        
        addSubview(bottomContainerView)
        bottomContainerView.addSubview(filterScrollView)
        filterScrollView.addSubview(filterContainerView)
        addSubview(loadingIndicator)
        
        // 设置约束
        // 移除模糊视图约束
        // blurView.snp.makeConstraints { $0.edges.equalToSuperview() }
        
        topContainerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(10)
            make.leading.trailing.equalToSuperview().inset(15)
            make.height.equalTo(40)
        }
        
        resetButton.snp.makeConstraints { make in
            make.leading.equalToSuperview()
            make.centerY.equalToSuperview()
            make.size.equalTo(30)
        }
        
        intensitySlider.snp.makeConstraints { make in
            make.leading.equalTo(resetButton.snp.trailing).offset(10)
            make.trailing.equalTo(originalLabel.snp.leading).offset(-10)
            make.centerY.equalToSuperview()
        }
        
        originalLabel.snp.makeConstraints { make in
            make.trailing.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(40)
        }
        
        valueLabel.snp.makeConstraints { make in
            make.centerX.equalTo(intensitySlider)
            make.bottom.equalTo(intensitySlider.snp.top).offset(-5)
            make.width.greaterThanOrEqualTo(30)
            make.height.equalTo(20)
        }
        
        bottomContainerView.snp.makeConstraints { make in
            make.top.equalTo(topContainerView.snp.bottom).offset(15)
            make.leading.trailing.equalToSuperview().inset(15)
            make.bottom.equalTo(self.safeAreaLayoutGuide.snp.bottom).inset(15)
        }

        filterScrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        filterContainerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalToSuperview()
        }
        
        loadingIndicator.snp.makeConstraints { make in
            make.center.equalTo(bottomContainerView)
        }
        
        // 确保valueLabel在最顶层
        bringSubviewToFront(valueLabel)
    }
    
    // 创建滑块的圆形图像
    private func createThumbImage(size: CGSize) -> UIImage {
        UIGraphicsBeginImageContextWithOptions(size, false, 0.0)
        
        let context = UIGraphicsGetCurrentContext()
        context?.setFillColor(UIColor.white.cgColor)
        
        let rect = CGRect(x: 0, y: 0, width: size.width, height: size.height)
        context?.fillEllipse(in: rect)
        
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        return image ?? UIImage()
    }
    
    // 创建滤镜按钮
    private func createFilterButtons() {
        // 清除现有的按钮
        for subview in filterContainerView.subviews {
            subview.removeFromSuperview()
        }

        guard !filterItems.isEmpty else { return }

        // 按钮尺寸
        let buttonSize: CGFloat = 55
        let spacing: CGFloat = 15
        let labelHeight: CGFloat = 20
        let itemHeight = buttonSize + labelHeight + 10
        let rowSpacing: CGFloat = 10 // 上下行之间的间距

        // 计算瀑布流布局：上下交替排列
        // 上面一行：1、3、5...（奇数位置，index % 2 == 0）
        // 下面一行：2、4、6...（偶数位置，index % 2 == 1）
        let topRowCount = (filterItems.count + 1) / 2  // 奇数个数
        let bottomRowCount = filterItems.count / 2     // 偶数个数
        let maxRowCount = max(topRowCount, bottomRowCount)

        // 计算总宽度和高度
        let totalWidth = CGFloat(maxRowCount) * (buttonSize + spacing) - spacing
        let totalHeight = itemHeight * 2 + rowSpacing

        // 设置滚动视图内容大小
        filterContainerView.snp.remakeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalToSuperview()
            make.width.equalTo(max(totalWidth, filterScrollView.frame.width))
            make.height.equalTo(totalHeight)
        }

        // 创建所有按钮（瀑布流排列）
        for (index, item) in filterItems.enumerated() {
            createFilterButton(for: item, at: index, buttonSize: buttonSize, spacing: spacing, labelHeight: labelHeight, itemHeight: itemHeight, rowSpacing: rowSpacing)
        }

        // 设置滚动视图内容大小
        filterScrollView.contentSize = CGSize(width: totalWidth, height: totalHeight)

        // 确保UI创建完成后，如果默认选中第一个滤镜，立即应用其效果
        if !filterItems.isEmpty && filterItems[0].isSelected {
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                // 确保第一个滤镜的效果被正确应用（清除其他滤镜效果）
                self.delegate?.filterPanelDidSelectFilter(self.filterItems[0])
            }
        }
    }
    
    // 创建单个滤镜按钮
    private func createFilterButton(for item: FilterItem, at index: Int, buttonSize: CGFloat, spacing: CGFloat, labelHeight: CGFloat, itemHeight: CGFloat, rowSpacing: CGFloat) {

        // 创建容器视图
        let itemContainer = UIView()
        itemContainer.tag = 100 + index
        filterContainerView.addSubview(itemContainer)

        // 计算瀑布流位置：上下交替排列
        var xOffset: CGFloat = 0
        var yOffset: CGFloat = 0

        if index % 2 == 0 {
            // 奇数位置（1、3、5...）放在上面一行
            let columnIndex = index / 2
            xOffset = CGFloat(columnIndex) * (buttonSize + spacing)
            yOffset = 0
        } else {
            // 偶数位置（2、4、6...）放在下面一行
            let columnIndex = (index - 1) / 2
            xOffset = CGFloat(columnIndex) * (buttonSize + spacing)
            yOffset = itemHeight + rowSpacing
        }

        // 设置容器位置
        itemContainer.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(xOffset)
            make.top.equalToSuperview().offset(yOffset)
            make.width.equalTo(buttonSize)
            make.height.equalTo(buttonSize + spacing + labelHeight)
        }
        
        // 创建滤镜缩略图按钮
        let button = UIButton(type: .custom)
        button.tag = 200 + index
        button.layer.cornerRadius = 12.5
        button.clipsToBounds = true
        button.layer.borderWidth = 0
        button.layer.borderColor = UIColor(hex: "#FF8F1F").cgColor
        button.addTarget(self, action: #selector(filterButtonTapped(_:)), for: .touchUpInside)
        itemContainer.addSubview(button)
        
        // 设置滤镜缩略图
        if let imageURL = URL(string: item.describeImg) {
            button.kf.setBackgroundImage(
                with: imageURL,
                for: .normal,
                placeholder: UIImage(named: "filter_placeholder"),
                options: [.transition(.fade(0.2))]
            )
        }
        
        // 设置按钮位置
        button.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(buttonSize)
        }
        
        // 创建滤镜名称标签
        let label = UILabel()
        label.tag = 300 + index
        label.text = item.name
        label.textColor = item.isSelected ? UIColor.white : UIColor.white.withAlphaComponent(0.7)
        label.font = item.isSelected ? UIFont.systemFont(ofSize: 12, weight: .medium) : UIFont.systemFont(ofSize: 12)
        label.textAlignment = .center
        itemContainer.addSubview(label)
        
        // 设置标签位置
        label.snp.makeConstraints { make in
            make.top.equalTo(button.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(labelHeight)
        }
        
        // 如果滤镜未下载，添加下载按钮
        if !item.isDownloaded {
            let downloadButton = UIButton(type: .custom)
            downloadButton.tag = 400 + index
            downloadButton.setImage(UIImage(named: "video_download_btn"), for: .normal)
            downloadButton.backgroundColor = UIColor.black.withAlphaComponent(0.5)
            downloadButton.layer.cornerRadius = 12
            downloadButton.addTarget(self, action: #selector(downloadButtonTapped(_:)), for: .touchUpInside)
            itemContainer.addSubview(downloadButton)
            
            // 设置下载按钮位置
            downloadButton.snp.makeConstraints { make in
//                make.top.equalTo(button).offset(6)
//                make.right.equalTo(button).offset(-6)
                make.center.equalTo(button)
                make.width.height.equalTo(30)
            }
            
            // 添加加载指示器
            let indicator = UIActivityIndicatorView(style: .medium)
            indicator.tag = 500 + index
            indicator.color = UIColor.white
            indicator.hidesWhenStopped = true
            itemContainer.addSubview(indicator)
            
            // 设置加载指示器位置
            indicator.snp.makeConstraints { make in
                make.center.equalTo(downloadButton)
            }
        }
        
        // 设置选中状态
        updateFilterButtonState(item: item, at: index)
    }
    
    // 更新滤镜按钮状态
    private func updateFilterButtonState(item: FilterItem, at index: Int) {
        guard let containerView = filterContainerView.viewWithTag(100 + index),
              let button = containerView.viewWithTag(200 + index),
              let label = containerView.viewWithTag(300 + index) as? UILabel else {
            return
        }

        // 更新按钮边框
        button.layer.borderWidth = item.isSelected ? 2 : 0

        // 更新标签
        label.textColor = item.isSelected ? UIColor.white : UIColor.white.withAlphaComponent(0.7)
        label.font = item.isSelected ? UIFont.systemFont(ofSize: 12, weight: .medium) : UIFont.systemFont(ofSize: 12)
    }
    
    // 更新所有滤镜按钮状态
    private func updateAllFilterButtonsState() {
        for (index, item) in filterItems.enumerated() {
            updateFilterButtonState(item: item, at: index)
        }
    }
    
    // 更新滑块值
    private func updateSliderValue() {
        guard let selectedId = selectedFilterId,
              let index = filterItems.firstIndex(where: { $0.id == selectedId }) else {
            return
        }
        
        intensitySlider.value = filterItems[index].intensity
    }
    
    // MARK: - Data Loading
    
    func fetchFilterList() {
        loadingIndicator.startAnimating()
        
        APIManager.shared.getFilterList(type: "4") { [weak self] result in
            guard let self = self else { return }
            
            self.loadingIndicator.stopAnimating()
            
            switch result {
            case .success(let response):
                if let data = response.data {
                    self.processFilterData(data)
                    // 添加预加载功能
                    self.preloadAllFilters()
                } else {
                    print("滤镜数据为空")
                }
            case .failure(let error):
                print("获取滤镜列表失败: \(error.localizedDescription)")
            }
        }
    }
    
    private func processFilterData(_ filterData: [VideoEditConfigDataItem]) {
        // 转换API数据到FilterItem数组
        filterItems = filterData.map { item in
            let isDownloaded = isFilterDownloaded(contentFile: item.contentFile!)
            
            return FilterItem(
                id: item.id!,
                name: item.name!,
                describeImg: item.describeImg!,
                contentFile: item.contentFile!,
                isDownloaded: isDownloaded,
                isSelected: false
            )
        }
        
        // 如果有滤镜，默认选中第一个（原图滤镜按钮）
        if !filterItems.isEmpty {
            filterItems[0].isSelected = true
            selectedFilterId = filterItems[0].id
            currentItemIndex = 0

            // 第一个滤镜禁用滑块
            intensitySlider.isEnabled = false
            intensitySlider.alpha = 0.5

            updateSliderValue()

            // 主动应用第一个滤镜效果，确保UI状态与实际效果同步
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                self.delegate?.filterPanelDidSelectFilter(self.filterItems[0])
            }
        }
        
        // 创建滤镜按钮
        createFilterButtons()
    }
    
    private func isFilterDownloaded(contentFile: String) -> Bool {
        // 获取文件名
        guard let fileName = URL(string: contentFile)?.lastPathComponent else {
            return false
        }
        
        // 检查本地Documents目录是否存在此文件
        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let fileURL = documentsDirectory.appendingPathComponent("Filters/\(fileName)")
        
        return FileManager.default.fileExists(atPath: fileURL.path)
    }
    
    // 预加载所有滤镜资源
    private func preloadAllFilters() {
        print("开始预加载所有滤镜，共\(filterItems.count)个")
        
        // 创建滤镜目录
        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let filterDirectory = documentsDirectory.appendingPathComponent("Filters")
        
        do {
            if !FileManager.default.fileExists(atPath: filterDirectory.path) {
                try FileManager.default.createDirectory(at: filterDirectory, withIntermediateDirectories: true, attributes: nil)
                print("创建滤镜目录成功: \(filterDirectory.path)")
            }
        } catch {
            print("创建滤镜目录失败: \(error)")
            return
        }
        
        // 优先下载第一个滤镜（如果有）
        if !filterItems.isEmpty {
            let firstItem = filterItems[0]
            if !firstItem.isDownloaded {
                print("优先下载第一个滤镜: \(firstItem.name)")
                if let url = URL(string: firstItem.contentFile) {
                    let fileName = url.lastPathComponent
                    let destinationURL = filterDirectory.appendingPathComponent(fileName)
                    
                    URLSession.shared.dataTask(with: url) { [weak self] data, response, error in
                        guard let self = self, let data = data, error == nil else {
                            print("预加载第一个滤镜失败: \(error?.localizedDescription ?? "未知错误")")
                            return
                        }
                        
                        do {
                            try data.write(to: destinationURL)
                            print("第一个滤镜预加载成功: \(destinationURL.path)")
                            
                            DispatchQueue.main.async {
                                // 更新滤镜状态
                                self.filterItems[0].isDownloaded = true
                                
                                // 如果已创建UI，更新按钮状态
                                if let containerView = self.filterContainerView.viewWithTag(100),
                                   let downloadButton = containerView.viewWithTag(400) {
                                    downloadButton.removeFromSuperview()
                                }
                                
                                // 如果第一个滤镜是选中状态，应用它
                                if self.filterItems[0].isSelected {
                                    self.applyFilter(at: 0)
                                }
                            }
                        } catch {
                            print("保存预加载滤镜失败: \(error)")
                        }
                    }.resume()
                }
            }
        }
        
        // 延迟下载其他滤镜
        DispatchQueue.global().async { [weak self] in
            guard let self = self else { return }
            
            // 下载其他滤镜
            for (index, item) in self.filterItems.enumerated() {
                if index == 0 || item.isDownloaded { continue } // 跳过第一个和已下载的
                
                if let url = URL(string: item.contentFile) {
                    let fileName = url.lastPathComponent
                    let destinationURL = filterDirectory.appendingPathComponent(fileName)
                    
                    // 使用简单的延迟避免同时发起太多请求
                    Thread.sleep(forTimeInterval: 0.5)
                    
                    URLSession.shared.dataTask(with: url) { data, response, error in
                        guard let data = data, error == nil else {
                            print("预加载滤镜 \(item.name) 失败: \(error?.localizedDescription ?? "未知错误")")
                            return
                        }
                        
                        do {
                            try data.write(to: destinationURL)
                            print("滤镜 \(item.name) 预加载成功")
                            
                            DispatchQueue.main.async {
                                // 更新滤镜状态
                                if let itemIndex = self.filterItems.firstIndex(where: { $0.id == item.id }) {
                                    self.filterItems[itemIndex].isDownloaded = true
                                    
                                    // 如果已创建UI，更新按钮状态
                                    if let containerView = self.filterContainerView.viewWithTag(100 + itemIndex),
                                       let downloadButton = containerView.viewWithTag(400 + itemIndex) {
                                        downloadButton.removeFromSuperview()
                                    }
                                }
                            }
                        } catch {
                            print("保存预加载滤镜 \(item.name) 失败: \(error)")
                        }
                    }.resume()
                }
            }
        }
    }
    
    // 下载滤镜文件
    private func downloadFilter(for index: Int) {
        guard index < filterItems.count else { return }
        
        let filterItem = filterItems[index]
        guard let url = URL(string: filterItem.contentFile) else { return }
        
        print("请求下载滤镜: \(filterItem.name), ID: \(filterItem.id)")
        
        // 获取文件名
        let fileName = url.lastPathComponent
        
        // 创建目标目录
        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let filterDirectory = documentsDirectory.appendingPathComponent("Filters")
        
        do {
            // 先检查目录是否存在，如果不存在再创建
            if !FileManager.default.fileExists(atPath: filterDirectory.path) {
                try FileManager.default.createDirectory(at: filterDirectory, withIntermediateDirectories: true, attributes: nil)
                print("创建滤镜目录成功: \(filterDirectory.path)")
            }
        } catch {
            print("创建滤镜目录失败: \(error)")
            return
        }
        
        let destinationURL = filterDirectory.appendingPathComponent(fileName)
        
        // 显示加载指示器
        if let containerView = filterContainerView.viewWithTag(100 + index),
           let downloadButton = containerView.viewWithTag(400 + index),
           let indicator = containerView.viewWithTag(500 + index) as? UIActivityIndicatorView {
            downloadButton.isHidden = true
            indicator.startAnimating()
        }
        
        // 使用URLSession的dataTask直接获取数据，而不是downloadTask
        let dataTask = URLSession.shared.dataTask(with: url) { [weak self] data, response, error in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                // 隐藏加载指示器
                if let containerView = self.filterContainerView.viewWithTag(100 + index),
                   let indicator = containerView.viewWithTag(500 + index) as? UIActivityIndicatorView {
                    indicator.stopAnimating()
                }
                
                if let error = error {
                    print("下载滤镜失败: \(error)")
                    // 显示下载按钮
                    if let containerView = self.filterContainerView.viewWithTag(100 + index),
                       let downloadButton = containerView.viewWithTag(400 + index) {
                        downloadButton.isHidden = false
                    }
                    return
                }
                
                guard let data = data, !data.isEmpty else {
                    print("下载滤镜失败: 数据为空")
                    // 显示下载按钮
                    if let containerView = self.filterContainerView.viewWithTag(100 + index),
                       let downloadButton = containerView.viewWithTag(400 + index) {
                        downloadButton.isHidden = false
                    }
                    return
                }
                
                do {
                    // 如果目标文件已存在，先删除
                    if FileManager.default.fileExists(atPath: destinationURL.path) {
                        try FileManager.default.removeItem(at: destinationURL)
                    }
                    
                    // 再次检查目标目录是否存在
                    if !FileManager.default.fileExists(atPath: filterDirectory.path) {
                        try FileManager.default.createDirectory(at: filterDirectory, withIntermediateDirectories: true, attributes: nil)
                        print("下载回调中创建滤镜目录成功: \(filterDirectory.path)")
                    }
                    
                    // 直接写入文件，不使用临时文件
                    try data.write(to: destinationURL)
                    
                    print("滤镜下载成功: \(destinationURL.path)")
                    
                    // 更新滤镜项状态
                    self.filterItems[index].isDownloaded = true
                    
                    // 移除下载按钮
                    if let containerView = self.filterContainerView.viewWithTag(100 + index),
                       let downloadButton = containerView.viewWithTag(400 + index) {
                        downloadButton.removeFromSuperview()
                    }
                    
                    // 如果是当前选中的滤镜，应用效果
                    if self.filterItems[index].isSelected {
                        self.applyFilter(at: index)
                    }
                    
                } catch {
                    print("保存滤镜文件失败: \(error)")
                    // 显示下载按钮
                    if let containerView = self.filterContainerView.viewWithTag(100 + index),
                       let downloadButton = containerView.viewWithTag(400 + index) {
                        downloadButton.isHidden = false
                    }
                }
            }
        }
        
        dataTask.resume()
    }
    
    // 应用滤镜
    private func applyFilter(at index: Int) {
        guard index < filterItems.count, filterItems[index].isDownloaded else { return }

        // 重置所有滤镜选中状态
        for i in 0..<filterItems.count {
            filterItems[i].isSelected = (i == index)
        }

        selectedFilterId = filterItems[index].id
        currentItemIndex = index

        // 特殊处理：如果选择第一个滤镜按钮（API下发的"原图"滤镜）
        if index == 0 {
            // 清除其他滤镜的强度设置（因为API只接受1种滤镜）
            for i in 1..<filterItems.count {
                filterItems[i].intensity = 0
            }
            // 第一个滤镜强制强度为0，不能调整
            filterItems[index].intensity = 0

            // 禁用滑块
            intensitySlider.isEnabled = false
            intensitySlider.alpha = 0.5
        } else {
            // 选择其他滤镜时：清除第一个滤镜的强度，当前滤镜从0开始
            filterItems[0].intensity = 0
            filterItems[index].intensity = 0

            // 启用滑块
            intensitySlider.isEnabled = true
            intensitySlider.alpha = 1.0
        }

        // 清除原始强度记录（不保存历史强度）
        originalIntensities.removeAll()

        // 更新UI
        updateAllFilterButtonsState()
        updateSliderValue()

        // 通知代理
        delegate?.filterPanelDidSelectFilter(filterItems[index])
    }
    
    // MARK: - Actions
    
    @objc private func resetButtonTapped() {
        guard let selectedId = selectedFilterId,
              let index = filterItems.firstIndex(where: { $0.id == selectedId }) else {
            return
        }

        // 第一个滤镜强制为0，不需要重置
        if index == 0 {
            return
        }

        // 重置强度为0
        filterItems[index].intensity = 0
        intensitySlider.value = 0

        // 更新原始强度记录
        originalIntensities[selectedId] = 0

        // 通知代理
        delegate?.filterPanelDidChangeIntensity(0, for: filterItems[index])
    }
    
    @objc private func sliderValueChanged(_ sender: UISlider) {
        guard let selectedId = selectedFilterId,
              let index = filterItems.firstIndex(where: { $0.id == selectedId }) else {
            return
        }
        
        // 更新强度值
        let value = sender.value

        // 第一个滤镜强制强度为0，不允许修改
        if index == 0 {
            filterItems[index].intensity = 0
            intensitySlider.value = 0
            return
        }

        filterItems[index].intensity = value

        // 保存到原始强度记录中
        originalIntensities[selectedId] = value

        // 更新值标签
        updateValueLabel(value, for: sender)

        // 通知代理
        delegate?.filterPanelDidChangeIntensity(value, for: filterItems[index])
        
        // 延迟隐藏值标签
        NSObject.cancelPreviousPerformRequests(withTarget: self, selector: #selector(hideValueLabel), object: nil)
        perform(#selector(hideValueLabel), with: nil, afterDelay: 1.0)
    }
    
    @objc private func sliderTouchEnded() {
        // 滑动结束后延迟隐藏值标签
        NSObject.cancelPreviousPerformRequests(withTarget: self, selector: #selector(hideValueLabel), object: nil)
        perform(#selector(hideValueLabel), with: nil, afterDelay: 0.5)
    }
    
    // 保存当前选中滤镜的强度
    private func saveCurrentFilterIntensity() {
        guard let selectedId = selectedFilterId,
              let index = filterItems.firstIndex(where: { $0.id == selectedId }) else { return }

        originalIntensities[selectedId] = filterItems[index].intensity
    }

    // 恢复当前选中滤镜的强度
    private func restoreCurrentFilterIntensity() {
        guard let selectedId = selectedFilterId,
              let index = filterItems.firstIndex(where: { $0.id == selectedId }),
              let originalIntensity = originalIntensities[selectedId] else { return }

        filterItems[index].intensity = originalIntensity
        intensitySlider.value = originalIntensity
    }

    // 清零当前选中滤镜的强度
    private func clearCurrentFilterIntensity() {
        guard let selectedId = selectedFilterId,
              let index = filterItems.firstIndex(where: { $0.id == selectedId }) else { return }

        filterItems[index].intensity = 0
        intensitySlider.value = 0
    }

    // 右上角原图标签长按事件（用于对比0强度和用户设置强度）
    @objc private func originalLabelLongPressed(_ gesture: UILongPressGestureRecognizer) {
        guard let selectedId = selectedFilterId,
              let index = filterItems.firstIndex(where: { $0.id == selectedId }) else { return }

        switch gesture.state {
        case .began:
            // 按住开始：保存当前滤镜强度并清零
            saveCurrentFilterIntensity()
            clearCurrentFilterIntensity()

            // 通知代理应用0强度的滤镜效果
            delegate?.filterPanelDidChangeIntensity(0, for: filterItems[index])
            delegate?.filterPanelDidTouchOriginal(.began)

        case .ended, .cancelled:
            // 松开：恢复当前滤镜的强度
            restoreCurrentFilterIntensity()

            // 通知代理恢复用户设置的滤镜强度
            delegate?.filterPanelDidChangeIntensity(filterItems[index].intensity, for: filterItems[index])
            delegate?.filterPanelDidTouchOriginal(.ended)

        default:
            break
        }
    }
    
    @objc private func filterButtonTapped(_ sender: UIButton) {
        let index = sender.tag - 200
        
        guard index >= 0, index < filterItems.count else { return }
        
        // 如果滤镜已下载，应用滤镜
        if filterItems[index].isDownloaded {
            applyFilter(at: index)
        } else {
            // 否则下载滤镜
            downloadFilter(for: index)
        }
    }
    
    @objc private func downloadButtonTapped(_ sender: UIButton) {
        let index = sender.tag - 400
        
        guard index >= 0, index < filterItems.count else { return }
        
        // 通知代理
        delegate?.filterPanelDidTapDownload(for: filterItems[index])
        
        // 开始下载
        downloadFilter(for: index)
    }
    
    @objc private func hideValueLabel() {
        valueLabel.isHidden = true
    }
    
    // 更新值标签
    private func updateValueLabel(_ value: Float, for slider: UISlider) {
        let intValue = Int(value * 100) // 转为0-100整数
        valueLabel.text = "\(intValue)"
        valueLabel.isHidden = false
        
        // 更新标签位置使其在滑块拇指上方
        let trackRect = slider.trackRect(forBounds: slider.bounds)
        let thumbRect = slider.thumbRect(forBounds: slider.bounds, trackRect: trackRect, value: value)
        let thumbCenterX = thumbRect.midX
        
        valueLabel.snp.remakeConstraints { make in
            make.centerX.equalTo(slider.snp.leading).offset(thumbCenterX)
            make.bottom.equalTo(slider.snp.top).offset(-5)
            make.width.greaterThanOrEqualTo(30)
            make.height.equalTo(20)
        }
        
        // 立即布局更新位置
        self.layoutIfNeeded()
    }

} 
